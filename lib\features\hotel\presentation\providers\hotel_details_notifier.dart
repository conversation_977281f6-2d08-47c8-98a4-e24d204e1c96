/// Hotel details state management using Riverpod StateNotifier
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Hotel details state class
class HotelDetailsState {
  final bool isLoading;
  final String? error;

  const HotelDetailsState({
    this.isLoading = false,
    this.error,
  });

  HotelDetailsState copyWith({
    bool? isLoading,
    String? error,
  }) {
    return HotelDetailsState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HotelDetailsState &&
        other.isLoading == isLoading &&
        other.error == error;
  }

  @override
  int get hashCode => Object.hash(isLoading, error);
}

/// Hotel details StateNotifier
class HotelDetailsNotifier extends StateNotifier<HotelDetailsState> {
  HotelDetailsNotifier() : super(const HotelDetailsState());

  /// Set loading state
  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  /// Set error
  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Hotel details provider
final hotelDetailsProvider = StateNotifierProvider<HotelDetailsNotifier, HotelDetailsState>(
  (ref) => HotelDetailsNotifier(),
);
