import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/features/search/data/models/recent_search.dart';
import 'package:kind_ali/features/home/<USER>/providers/home_notifier.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';

class RecentSearchWidget extends ConsumerWidget {
  const RecentSearchWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeState = ref.watch(homeProvider);

    if (homeState.recentSearches.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'search.recent_searches'.tr,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.text,
                ),
              ),
              TextButton(
                onPressed: () => ref.read(homeProvider.notifier).clearRecentSearches(),
                child: Text(
                  'search.clear'.tr,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 90,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            itemCount: homeState.recentSearches.length,
            separatorBuilder: (context, index) => const SizedBox(width: 12),
            itemBuilder: (context, index) {
              final search = homeState.recentSearches[index];
              return _buildRecentSearchCard(context, search, ref);
            },
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildRecentSearchCard(BuildContext context, RecentSearch search, WidgetRef ref) {
    return InkWell(
      onTap: () {
        ref.read(homeProvider.notifier).applyRecentSearch(search);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Applied search for ${search.destination}'),
            backgroundColor: AppColors.primary,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            duration: const Duration(seconds: 2),
          ),
        );
      },
      child: Container(
        width: 280,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              spreadRadius: 1,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: AppColors.primary.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Destination
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: 16,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    search.destination,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.text,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),   Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: AppColors.accent.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '${search.numberOfNights} night${search.numberOfNights > 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // Dates
            Row(mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 14,
                  color: AppColors.textLight,
                ),
                const SizedBox(width: 4),
                Text(
                  _formatDateRange(search.checkInDate, search.checkOutDate),
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textLight,
                  ),
                ),
              Text('-'),
              
                Text(
                  search.guestSummary,
                  style: TextStyle(
                    fontSize: 13,
                    color: AppColors.textLight,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
             
              ],
            ),
          
            
           
          ],
        ),
      ),
    );
  }

  String _formatDateRange(DateTime checkIn, DateTime checkOut) {
    final formatter = DateFormat('MMM dd');
    final checkInStr = formatter.format(checkIn);
    final checkOutStr = formatter.format(checkOut);
    
    if (checkIn.year == checkOut.year) {
      return '$checkInStr - $checkOutStr';
    } else {
      final formatterWithYear = DateFormat('MMM dd, yyyy');
      return '${formatterWithYear.format(checkIn)} - ${formatterWithYear.format(checkOut)}';
    }
  }
}
