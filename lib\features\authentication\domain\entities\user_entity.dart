import 'package:equatable/equatable.dart';
import 'package:kind_ali/core/utils/enums.dart';

/// User entity representing the business model for users
class UserEntity extends Equatable {
  final String? userId;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final String? profileImageUrl;
  final DateTime? dateOfBirth;
  final String? nationality;
  final String? passportNumber;
  final String? preferredLanguage;
  final String? preferredCurrency;
  final bool? isEmailVerified;
  final bool? isPhoneVerified;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<String>? wishlistHotelIds;

  const UserEntity({
    this.userId,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.profileImageUrl,
    this.dateOfBirth,
    this.nationality,
    this.passportNumber,
    this.preferredLanguage,
    this.preferredCurrency,
    this.isEmailVerified,
    this.isPhoneVerified,
    this.createdAt,
    this.updatedAt,
    this.wishlistHotelIds,
  });

  /// Get full name
  String get fullName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    } else if (firstName != null) {
      return firstName!;
    } else if (lastName != null) {
      return lastName!;
    }
    return 'User';
  }

  /// Check if profile is complete
  bool get isProfileComplete {
    return firstName != null &&
        lastName != null &&
        email != null &&
        phone != null &&
        dateOfBirth != null;
  }

  @override
  List<Object?> get props => [
        userId,
        firstName,
        lastName,
        email,
        phone,
        profileImageUrl,
        dateOfBirth,
        nationality,
        passportNumber,
        preferredLanguage,
        preferredCurrency,
        isEmailVerified,
        isPhoneVerified,
        createdAt,
        updatedAt,
        wishlistHotelIds,
      ];
}

/// Authentication entity
class AuthEntity extends Equatable {
  final String? accessToken;
  final String? refreshToken;
  final DateTime? expiresAt;
  final UserEntity? user;
  final AuthStatus? status;

  const AuthEntity({
    this.accessToken,
    this.refreshToken,
    this.expiresAt,
    this.user,
    this.status,
  });

  /// Check if token is expired
  bool get isTokenExpired {
    if (expiresAt == null) return true;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Check if user is authenticated
  bool get isAuthenticated {
    return accessToken != null && !isTokenExpired && user != null;
  }

  @override
  List<Object?> get props => [
        accessToken,
        refreshToken,
        expiresAt,
        user,
        status,
      ];
}
