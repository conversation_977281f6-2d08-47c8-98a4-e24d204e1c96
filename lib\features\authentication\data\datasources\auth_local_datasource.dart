import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:kind_ali/core/constants/app_constants.dart';
import 'package:kind_ali/core/error/exceptions.dart';
import 'package:kind_ali/core/utils/enums.dart';
import 'package:kind_ali/features/authentication/data/models/user_model.dart';
import 'package:kind_ali/features/authentication/data/models/auth_model.dart';

/// Abstract class for authentication local data source
abstract class AuthLocalDataSource {
  /// Save authentication data
  Future<void> saveAuthData(AuthModel authModel);
  
  /// Get cached authentication data
  Future<AuthModel?> getCachedAuthData();

  /// Get authentication data (alias for getCachedAuthData)
  Future<AuthModel?> getAuthData();

  /// Clear authentication data
  Future<void> clearAuthData();
  
  /// Check if user is logged in
  Future<bool> isLoggedIn();
  
  /// Save user data
  Future<void> saveUserData(UserModel userModel);
  
  /// Get cached user data
  Future<UserModel?> getCachedUserData();
  
  /// Update login status
  Future<void> updateLoginStatus(bool isLoggedIn);
  
  /// Save login method
  Future<void> saveLoginMethod(String method);
  
  /// Get login method
  Future<String?> getLoginMethod();

  /// Login with email
  Future<AuthModel> loginWithEmail({required String email, required String password});

  /// Login with phone
  Future<AuthModel> loginWithPhone({required String phone, required String otp});

  /// Register with email
  Future<AuthModel> registerWithEmail({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
  });

  /// Register with phone
  Future<AuthModel> registerWithPhone({
    required String firstName,
    required String lastName,
    required String phone,
  });

  /// Simulate OTP sending
  Future<void> simulateOtpSending(String phone);

  /// Verify OTP
  Future<bool> verifyOtp({required String phone, required String otp});
}

/// Implementation of authentication local data source
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final SharedPreferences sharedPreferences;
  
  AuthLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<void> saveAuthData(AuthModel authModel) async {
    try {
      // Save auth token
      if (authModel.accessToken != null) {
        await sharedPreferences.setString(
          AppConstants.keyAuthToken,
          authModel.accessToken!,
        );
      }
      
      // Save refresh token
      if (authModel.refreshToken != null) {
        await sharedPreferences.setString(
          AppConstants.keyRefreshToken,
          authModel.refreshToken!,
        );
      }
      
      // Save user data
      if (authModel.user != null) {
        final userModel = UserModel.fromEntity(authModel.user!);
        await saveUserData(userModel);
      }
      
      // Update login status
      await updateLoginStatus(true);
      
      // Save expiry time
      if (authModel.expiresAt != null) {
        await sharedPreferences.setInt(
          'auth_expires_at',
          authModel.expiresAt!.millisecondsSinceEpoch,
        );
      }
    } catch (e) {
      throw CacheException('Failed to save auth data: $e');
    }
  }

  @override
  Future<AuthModel?> getCachedAuthData() async {
    try {
      final accessToken = sharedPreferences.getString(AppConstants.keyAuthToken);
      final refreshToken = sharedPreferences.getString(AppConstants.keyRefreshToken);
      final expiryTimestamp = sharedPreferences.getInt('auth_expires_at');
      final userData = await getCachedUserData();
      
      if (accessToken == null) return null;
      
      return AuthModel(
        accessToken: accessToken,
        refreshToken: refreshToken,
        expiresAt: expiryTimestamp != null 
            ? DateTime.fromMillisecondsSinceEpoch(expiryTimestamp)
            : null,
        user: userData,
      );
    } catch (e) {
      return null;
    }
  }

  @override
  Future<AuthModel?> getAuthData() async {
    return await getCachedAuthData();
  }

  @override
  Future<void> clearAuthData() async {
    try {
      await Future.wait([
        sharedPreferences.remove(AppConstants.keyAuthToken),
        sharedPreferences.remove(AppConstants.keyRefreshToken),
        sharedPreferences.remove(AppConstants.keyIsLoggedIn),
        sharedPreferences.remove(AppConstants.keyUserEmail),
        sharedPreferences.remove(AppConstants.keyUserPhone),
        sharedPreferences.remove(AppConstants.keyUserName),
        sharedPreferences.remove(AppConstants.keyLoginMethod),
        sharedPreferences.remove('auth_expires_at'),
        sharedPreferences.remove('cached_user_data'),
      ]);
    } catch (e) {
      throw CacheException('Failed to clear auth data: $e');
    }
  }

  @override
  Future<bool> isLoggedIn() async {
    try {
      final isLoggedIn = sharedPreferences.getBool(AppConstants.keyIsLoggedIn) ?? false;
      final authData = await getCachedAuthData();
      
      // Check if token exists and is not expired
      if (isLoggedIn && authData != null && authData.accessToken != null) {
        if (authData.expiresAt != null && DateTime.now().isAfter(authData.expiresAt!)) {
          // Token expired, clear auth data
          await clearAuthData();
          return false;
        }
        return true;
      }
      
      return false;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> saveUserData(UserModel userModel) async {
    try {
      final userJson = json.encode(userModel.toJson());
      await sharedPreferences.setString('cached_user_data', userJson);
      
      // Save individual fields for backward compatibility
      if (userModel.email != null) {
        await sharedPreferences.setString(AppConstants.keyUserEmail, userModel.email!);
      }
      if (userModel.phone != null) {
        await sharedPreferences.setString(AppConstants.keyUserPhone, userModel.phone!);
      }
      if (userModel.firstName != null) {
        await sharedPreferences.setString(AppConstants.keyUserName, userModel.firstName!);
      }
    } catch (e) {
      throw CacheException('Failed to save user data: $e');
    }
  }

  @override
  Future<UserModel?> getCachedUserData() async {
    try {
      final userJson = sharedPreferences.getString('cached_user_data');
      if (userJson != null) {
        final userMap = json.decode(userJson) as Map<String, dynamic>;
        return UserModel.fromJson(userMap);
      }
      
      // Fallback to individual fields for backward compatibility
      final email = sharedPreferences.getString(AppConstants.keyUserEmail);
      final phone = sharedPreferences.getString(AppConstants.keyUserPhone);
      final name = sharedPreferences.getString(AppConstants.keyUserName);
      
      if (email != null || phone != null) {
        return UserModel(
          email: email,
          phone: phone,
          firstName: name,
        );
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> updateLoginStatus(bool isLoggedIn) async {
    try {
      await sharedPreferences.setBool(AppConstants.keyIsLoggedIn, isLoggedIn);
    } catch (e) {
      throw CacheException('Failed to update login status: $e');
    }
  }

  @override
  Future<void> saveLoginMethod(String method) async {
    try {
      await sharedPreferences.setString(AppConstants.keyLoginMethod, method);
    } catch (e) {
      throw CacheException('Failed to save login method: $e');
    }
  }

  @override
  Future<String?> getLoginMethod() async {
    try {
      return sharedPreferences.getString(AppConstants.keyLoginMethod);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<AuthModel> loginWithEmail({required String email, required String password}) async {
    try {
      // Simulate local email login
      // In a real app, this would validate against stored credentials
      final user = UserModel(
        userId: '1',
        firstName: 'User',
        lastName: 'Name',
        email: email,
        phone: null,
        isEmailVerified: true,
        isPhoneVerified: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final authModel = AuthModel(
        accessToken: 'local_token_${DateTime.now().millisecondsSinceEpoch}',
        refreshToken: 'local_refresh_${DateTime.now().millisecondsSinceEpoch}',
        expiresAt: DateTime.now().add(const Duration(hours: 24)),
        user: user,
        status: AuthStatus.authenticated,
      );

      await saveAuthData(authModel);
      await saveLoginMethod('email');

      return authModel;
    } catch (e) {
      throw AuthException('Email login failed: $e');
    }
  }

  @override
  Future<AuthModel> loginWithPhone({required String phone, required String otp}) async {
    try {
      // Simulate local phone login with OTP verification
      // In a real app, this would validate the OTP
      if (otp != '123456') {
        throw AuthException('Invalid OTP');
      }

      final user = UserModel(
        userId: '1',
        firstName: 'User',
        lastName: 'Name',
        email: null,
        phone: phone,
        isEmailVerified: false,
        isPhoneVerified: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final authModel = AuthModel(
        accessToken: 'local_token_${DateTime.now().millisecondsSinceEpoch}',
        refreshToken: 'local_refresh_${DateTime.now().millisecondsSinceEpoch}',
        expiresAt: DateTime.now().add(const Duration(hours: 24)),
        user: user,
        status: AuthStatus.authenticated,
      );

      await saveAuthData(authModel);
      await saveLoginMethod('phone');

      return authModel;
    } catch (e) {
      throw AuthException('Phone login failed: $e');
    }
  }

  @override
  Future<AuthModel> registerWithEmail({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
  }) async {
    try {
      // Simulate local email registration
      final user = UserModel(
        userId: DateTime.now().millisecondsSinceEpoch.toString(),
        firstName: firstName,
        lastName: lastName,
        email: email,
        phone: null,
        isEmailVerified: false, // Would need email verification
        isPhoneVerified: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final authModel = AuthModel(
        accessToken: 'local_token_${DateTime.now().millisecondsSinceEpoch}',
        refreshToken: 'local_refresh_${DateTime.now().millisecondsSinceEpoch}',
        expiresAt: DateTime.now().add(const Duration(hours: 24)),
        user: user,
        status: AuthStatus.authenticated,
      );

      await saveAuthData(authModel);
      await saveLoginMethod('email');

      return authModel;
    } catch (e) {
      throw AuthException('Email registration failed: $e');
    }
  }

  @override
  Future<AuthModel> registerWithPhone({
    required String firstName,
    required String lastName,
    required String phone,
  }) async {
    try {
      // Simulate local phone registration
      final user = UserModel(
        userId: DateTime.now().millisecondsSinceEpoch.toString(),
        firstName: firstName,
        lastName: lastName,
        email: null,
        phone: phone,
        isEmailVerified: false,
        isPhoneVerified: false, // Would need OTP verification
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final authModel = AuthModel(
        accessToken: 'local_token_${DateTime.now().millisecondsSinceEpoch}',
        refreshToken: 'local_refresh_${DateTime.now().millisecondsSinceEpoch}',
        expiresAt: DateTime.now().add(const Duration(hours: 24)),
        user: user,
        status: AuthStatus.authenticated,
      );

      await saveAuthData(authModel);
      await saveLoginMethod('phone');

      return authModel;
    } catch (e) {
      throw AuthException('Phone registration failed: $e');
    }
  }

  @override
  Future<void> simulateOtpSending(String phone) async {
    try {
      // Simulate OTP sending by storing a timestamp
      await sharedPreferences.setInt(
        'otp_sent_${phone}',
        DateTime.now().millisecondsSinceEpoch,
      );
      // In a real app, this would trigger SMS/call
    } catch (e) {
      throw CacheException('Failed to simulate OTP sending: $e');
    }
  }

  @override
  Future<bool> verifyOtp({required String phone, required String otp}) async {
    try {
      // Simple OTP verification - in real app this would be more secure
      final sentTimestamp = sharedPreferences.getInt('otp_sent_${phone}');
      if (sentTimestamp == null) {
        return false; // OTP not sent
      }

      final sentTime = DateTime.fromMillisecondsSinceEpoch(sentTimestamp);
      final now = DateTime.now();

      // Check if OTP is expired (5 minutes)
      if (now.difference(sentTime).inMinutes > 5) {
        return false;
      }

      // For demo purposes, accept '123456' as valid OTP
      return otp == '123456';
    } catch (e) {
      return false;
    }
  }
}
