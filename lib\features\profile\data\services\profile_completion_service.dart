import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/profile_completion_model.dart';

/// Service for managing profile completion data in SharedPreferences
class ProfileCompletionService {
  static const String _keyProfileCompletion = 'profile_completion_data';
  static const String _keyIsNewUser = 'is_new_user';
  
  /// Get profile completion data from SharedPreferences
  static Future<ProfileCompletionModel> getProfileData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_keyProfileCompletion);
      
      if (jsonString == null || jsonString.isEmpty) {
        return ProfileCompletionModel.empty();
      }
      
      final jsonMap = json.decode(jsonString) as Map<String, dynamic>;
      return ProfileCompletionModel.fromJson(jsonMap);
    } catch (e) {
      // If there's any error reading data, return empty profile
      return ProfileCompletionModel.empty();
    }
  }

  /// Save profile completion data to SharedPreferences
  static Future<bool> saveProfileData(ProfileCompletionModel profileData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final updatedProfile = profileData.copyWith(
        lastUpdated: DateTime.now(),
        isProfileComplete: profileData.hasMinimumRequiredData,
      );
      
      final jsonString = json.encode(updatedProfile.toJson());
      final success = await prefs.setString(_keyProfileCompletion, jsonString);
      
      // Mark user as no longer new if they have saved some data
      if (success && !updatedProfile.isNewUser) {
        await prefs.setBool(_keyIsNewUser, false);
      }
      
      return success;
    } catch (e) {
      return false;
    }
  }

  /// Update specific fields in profile data
  static Future<bool> updateProfileFields({
    String? fullName,
    String? email,
    String? phone,
    String? dateOfBirth,
    String? gender,
    String? address,
    String? city,
    String? country,
    String? profileImagePath,
  }) async {
    try {
      final currentProfile = await getProfileData();
      final updatedProfile = currentProfile.copyWith(
        fullName: fullName,
        email: email,
        phone: phone,
        dateOfBirth: dateOfBirth,
        gender: gender,
        address: address,
        city: city,
        country: country,
        profileImagePath: profileImagePath,
      );
      
      return await saveProfileData(updatedProfile);
    } catch (e) {
      return false;
    }
  }

  /// Check if user is new (first time using the app)
  static Future<bool> isNewUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileData = await getProfileData();
      
      // Check both the explicit flag and if profile is empty
      final isNewUserFlag = prefs.getBool(_keyIsNewUser) ?? true;
      return isNewUserFlag && profileData.isNewUser;
    } catch (e) {
      return true; // Default to new user if there's an error
    }
  }

  /// Mark user as no longer new
  static Future<bool> markUserAsExisting() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setBool(_keyIsNewUser, false);
    } catch (e) {
      return false;
    }
  }

  /// Check if profile completion is required
  static Future<bool> isProfileCompletionRequired() async {
    try {
      final profileData = await getProfileData();
      return !profileData.hasMinimumRequiredData;
    } catch (e) {
      return true; // Default to requiring completion if there's an error
    }
  }

  /// Get profile completion status
  static Future<ProfileCompletionStatus> getCompletionStatus() async {
    try {
      final profileData = await getProfileData();
      final isNew = await isNewUser();
      
      if (isNew) {
        return ProfileCompletionStatus.newUser;
      } else if (!profileData.hasMinimumRequiredData) {
        return ProfileCompletionStatus.incompleteProfile;
      } else {
        return ProfileCompletionStatus.completeProfile;
      }
    } catch (e) {
      return ProfileCompletionStatus.newUser;
    }
  }

  /// Clear all profile completion data
  static Future<bool> clearProfileData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final success1 = await prefs.remove(_keyProfileCompletion);
      final success2 = await prefs.remove(_keyIsNewUser);
      return success1 && success2;
    } catch (e) {
      return false;
    }
  }

  /// Get all stored data as a formatted string for debugging
  static Future<String> getStoredDataAsString() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileData = await getProfileData();
      final isNew = await isNewUser();
      
      final buffer = StringBuffer();
      buffer.writeln('=== PROFILE COMPLETION DATA ===');
      buffer.writeln('Is New User: $isNew');
      buffer.writeln('Profile Complete: ${profileData.isProfileComplete}');
      buffer.writeln('Completion Percentage: ${profileData.completionPercentage.toStringAsFixed(1)}%');
      buffer.writeln('');
      buffer.writeln('--- PROFILE FIELDS ---');
      buffer.writeln('Full Name: ${profileData.fullName ?? 'Not set'}');
      buffer.writeln('Email: ${profileData.email ?? 'Not set'}');
      buffer.writeln('Phone: ${profileData.phone ?? 'Not set'}');
      buffer.writeln('Date of Birth: ${profileData.dateOfBirth ?? 'Not set'}');
      buffer.writeln('Gender: ${profileData.gender ?? 'Not set'}');
      buffer.writeln('Address: ${profileData.address ?? 'Not set'}');
      buffer.writeln('City: ${profileData.city ?? 'Not set'}');
      buffer.writeln('Country: ${profileData.country ?? 'Not set'}');
      buffer.writeln('Profile Image: ${profileData.profileImagePath ?? 'Not set'}');
      buffer.writeln('');
      buffer.writeln('--- MISSING FIELDS ---');
      buffer.writeln('Required: ${profileData.missingRequiredFields.join(', ')}');
      buffer.writeln('Optional: ${profileData.missingOptionalFields.join(', ')}');
      buffer.writeln('');
      buffer.writeln('Last Updated: ${profileData.lastUpdated?.toString() ?? 'Never'}');
      
      return buffer.toString();
    } catch (e) {
      return 'Error retrieving profile data: $e';
    }
  }

  /// Get all SharedPreferences keys and values for debugging
  static Future<Map<String, dynamic>> getAllSharedPreferencesData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final data = <String, dynamic>{};
      
      for (final key in keys) {
        final value = prefs.get(key);
        data[key] = value;
      }
      
      return data;
    } catch (e) {
      return {'error': e.toString()};
    }
  }
}

/// Enum for profile completion status
enum ProfileCompletionStatus {
  newUser,
  incompleteProfile,
  completeProfile,
}

/// Extension for ProfileCompletionStatus
extension ProfileCompletionStatusExtension on ProfileCompletionStatus {
  String get description {
    switch (this) {
      case ProfileCompletionStatus.newUser:
        return 'New user - needs profile completion';
      case ProfileCompletionStatus.incompleteProfile:
        return 'Existing user - profile incomplete';
      case ProfileCompletionStatus.completeProfile:
        return 'Existing user - profile complete';
    }
  }
  
  bool get requiresProfileCompletion {
    return this != ProfileCompletionStatus.completeProfile;
  }
}
