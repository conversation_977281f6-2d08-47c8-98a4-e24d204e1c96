/// Booking providers for Riverpod dependency injection
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/providers/core_providers.dart';
import '../../data/datasources/booking_local_datasource.dart';
import '../../data/repositories/booking_repository_impl.dart';
import '../../domain/repositories/booking_repository.dart';
import '../../domain/usecases/booking/cancel_booking.dart';
import '../../domain/usecases/booking/check_room_availability.dart';
import '../../domain/usecases/booking/create_booking.dart';
import '../../domain/usecases/booking/get_booking_by_id.dart';
import '../../domain/usecases/booking/get_booking_history.dart';
import '../../domain/usecases/booking/get_upcoming_bookings.dart';

part 'booking_providers.g.dart';

// Data Sources
@riverpod
Future<BookingLocalDataSource> bookingLocalDataSource(Ref ref) async {
  final sharedPrefs = await ref.watch(sharedPreferencesProvider.future);
  return BookingLocalDataSourceImpl(sharedPreferences: sharedPrefs);
}

// Repository
@riverpod
Future<BookingRepository> bookingRepository(Ref ref) async {
  final localDataSource = await ref.watch(bookingLocalDataSourceProvider.future);
  final networkInfo = ref.watch(networkInfoProvider);

  return BookingRepositoryImpl(
    localDataSource: localDataSource,
    networkInfo: networkInfo,
  );
}

// Use Cases
@riverpod
Future<CheckRoomAvailability> checkRoomAvailability(Ref ref) async {
  final repository = await ref.watch(bookingRepositoryProvider.future);
  return CheckRoomAvailability(repository);
}

@riverpod
Future<CreateBooking> createBooking(Ref ref) async {
  final repository = await ref.watch(bookingRepositoryProvider.future);
  return CreateBooking(repository);
}

@riverpod
Future<GetBookingById> getBookingById(Ref ref) async {
  final repository = await ref.watch(bookingRepositoryProvider.future);
  return GetBookingById(repository);
}

@riverpod
Future<GetBookingHistory> getBookingHistory(Ref ref) async {
  final repository = await ref.watch(bookingRepositoryProvider.future);
  return GetBookingHistory(repository);
}

@riverpod
Future<GetUpcomingBookings> getUpcomingBookings(Ref ref) async {
  final repository = await ref.watch(bookingRepositoryProvider.future);
  return GetUpcomingBookings(repository);
}

@riverpod
Future<CancelBooking> cancelBooking(Ref ref) async {
  final repository = await ref.watch(bookingRepositoryProvider.future);
  return CancelBooking(repository);
}
