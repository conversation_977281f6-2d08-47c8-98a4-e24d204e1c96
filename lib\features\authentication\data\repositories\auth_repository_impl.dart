﻿import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import 'package:kind_ali/core/error/failures.dart';
import 'package:kind_ali/core/network/network_info.dart';
import '../../domain/entities/user_entity.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_local_datasource.dart';
import '../datasources/auth_remote_datasource.dart';
import '../models/auth_model.dart';
import '../models/user_model.dart';

/// Implementation of authentication repository
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;
  final AuthLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, AuthEntity>> loginWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      // For now, use local authentication since API is not available
      // Try remote first (will fail and fall back to local)
      if (await networkInfo.isConnected) {
        try {
          final remoteAuth = await remoteDataSource.loginWithEmail(
            email: email,
            password: password,
          );
          await localDataSource.saveAuthData(remoteAuth);
          return Right(remoteAuth.toEntity());
        } catch (e) {
          // Remote failed, use local authentication
          return await _performLocalEmailLogin(email, password);
        }
      } else {
        // No internet, use local authentication
        return await _performLocalEmailLogin(email, password);
      }
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, AuthEntity>> loginWithPhone({
    required String phone,
    required String otp,
  }) async {
    try {
      // For now, use local authentication since API is not available
      if (await networkInfo.isConnected) {
        try {
          final remoteAuth = await remoteDataSource.loginWithPhone(
            phone: phone,
            otp: otp,
          );
          await localDataSource.saveAuthData(remoteAuth);
          return Right(remoteAuth.toEntity());
        } catch (e) {
          // Remote failed, use local authentication
          return await _performLocalPhoneLogin(phone, otp);
        }
      } else {
        // No internet, use local authentication
        return await _performLocalPhoneLogin(phone, otp);
      }
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, AuthEntity>> registerWithEmail({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
  }) async {
    try {
      // For now, use local registration since API is not available
      if (await networkInfo.isConnected) {
        try {
          final remoteAuth = await remoteDataSource.registerWithEmail(
            firstName: firstName,
            lastName: lastName,
            email: email,
            password: password,
          );
          await localDataSource.saveAuthData(remoteAuth);
          return Right(remoteAuth.toEntity());
        } catch (e) {
          // Remote failed, use local registration
          return await _performLocalEmailRegistration(firstName, lastName, email, password);
        }
      } else {
        // No internet, use local registration
        return await _performLocalEmailRegistration(firstName, lastName, email, password);
      }
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, AuthEntity>> registerWithPhone({
    required String firstName,
    required String lastName,
    required String phone,
  }) async {
    try {
      // For now, use local registration since API is not available
      if (await networkInfo.isConnected) {
        try {
          final remoteAuth = await remoteDataSource.registerWithPhone(
            firstName: firstName,
            lastName: lastName,
            phone: phone,
          );
          await localDataSource.saveAuthData(remoteAuth);
          return Right(remoteAuth.toEntity());
        } catch (e) {
          // Remote failed, use local registration
          return await _performLocalPhoneRegistration(firstName, lastName, phone);
        }
      } else {
        // No internet, use local registration
        return await _performLocalPhoneRegistration(firstName, lastName, phone);
      }
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, void>> sendOtp(String phone) async {
    try {
      if (await networkInfo.isConnected) {
        try {
          await remoteDataSource.sendOtp(phone);
          return const Right(null);
        } catch (e) {
          // Remote failed, simulate local OTP sending
          return await _simulateOtpSending(phone);
        }
      } else {
        // No internet, simulate local OTP sending
        return await _simulateOtpSending(phone);
      }
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, bool>> verifyOtp({
    required String phone,
    required String otp,
  }) async {
    try {
      if (await networkInfo.isConnected) {
        try {
          final result = await remoteDataSource.verifyOtp(phone: phone, otp: otp);
          return Right(result);
        } catch (e) {
          // Remote failed, use local OTP verification
          return await _performLocalOtpVerification(phone, otp);
        }
      } else {
        // No internet, use local OTP verification
        return await _performLocalOtpVerification(phone, otp);
      }
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, void>> logout() async {
    try {
      final authData = await localDataSource.getAuthData();
      if (authData != null && await networkInfo.isConnected) {
        try {
          await remoteDataSource.logout(authData.accessToken ?? '');
        } catch (e) {
          // Remote logout failed, continue with local logout
        }
      }
      
      // Always perform local logout
      await localDataSource.clearAuthData();
      return const Right(null);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, AuthEntity?>> getCurrentAuth() async {
    try {
      final authData = await localDataSource.getAuthData();
      if (authData != null) {
        // Check if token is still valid
        if (authData.isTokenValid()) {
          return Right(authData.toEntity());
        } else {
          // Token expired, try to refresh
          return await refreshToken();
        }
      }
      return const Right(null);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, AuthEntity>> refreshToken() async {
    try {
      final authData = await localDataSource.getAuthData();
      if (authData?.refreshToken == null) {
        return const Left(AuthFailure('No refresh token available'));
      }

      if (await networkInfo.isConnected) {
        try {
          final refreshedAuth = await remoteDataSource.refreshToken(authData!.refreshToken!);
          await localDataSource.saveAuthData(refreshedAuth);
          return Right(refreshedAuth.toEntity());
        } catch (e) {
          // Remote refresh failed, clear local auth
          await localDataSource.clearAuthData();
          return Left(_mapExceptionToFailure(e));
        }
      } else {
        // No internet, can't refresh token
        await localDataSource.clearAuthData();
        return const Left(NetworkFailure('Cannot refresh token without internet connection'));
      }
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, bool>> isLoggedIn() async {
    try {
      final authResult = await getCurrentAuth();
      return authResult.fold(
        (failure) => const Right(false),
        (auth) => Right(auth != null),
      );
    } catch (e) {
      return const Right(false);
    }
  }

  // Local authentication helper methods
  Future<Either<Failure, AuthEntity>> _performLocalEmailLogin(String email, String password) async {
    try {
      final authData = await localDataSource.loginWithEmail(email: email, password: password);
      return Right(authData.toEntity());
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  Future<Either<Failure, AuthEntity>> _performLocalPhoneLogin(String phone, String otp) async {
    try {
      final authData = await localDataSource.loginWithPhone(phone: phone, otp: otp);
      return Right(authData.toEntity());
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  Future<Either<Failure, AuthEntity>> _performLocalEmailRegistration(
    String firstName, String lastName, String email, String password) async {
    try {
      final authData = await localDataSource.registerWithEmail(
        firstName: firstName,
        lastName: lastName,
        email: email,
        password: password,
      );
      return Right(authData.toEntity());
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  Future<Either<Failure, AuthEntity>> _performLocalPhoneRegistration(
    String firstName, String lastName, String phone) async {
    try {
      final authData = await localDataSource.registerWithPhone(
        firstName: firstName,
        lastName: lastName,
        phone: phone,
      );
      return Right(authData.toEntity());
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  Future<Either<Failure, void>> _simulateOtpSending(String phone) async {
    try {
      await localDataSource.simulateOtpSending(phone);
      return const Right(null);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  Future<Either<Failure, bool>> _performLocalOtpVerification(String phone, String otp) async {
    try {
      final result = await localDataSource.verifyOtp(phone: phone, otp: otp);
      return Right(result);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, void>> forgotPassword(String email) async {
    try {
      if (await networkInfo.isConnected) {
        try {
          await remoteDataSource.forgotPassword(email);
          return const Right(null);
        } catch (e) {
          // Remote failed, simulate local forgot password
          return await _simulateLocalForgotPassword(email);
        }
      } else {
        // No internet, simulate local forgot password
        return await _simulateLocalForgotPassword(email);
      }
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, void>> resetPassword({
    required String email,
    required String token,
    required String newPassword,
  }) async {
    try {
      if (await networkInfo.isConnected) {
        try {
          await remoteDataSource.resetPassword(
            email: email,
            token: token,
            newPassword: newPassword,
          );
          return const Right(null);
        } catch (e) {
          // Remote failed, simulate local reset password
          return await _simulateLocalResetPassword(email, token, newPassword);
        }
      } else {
        // No internet, simulate local reset password
        return await _simulateLocalResetPassword(email, token, newPassword);
      }
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, void>> saveAuthData(AuthEntity authEntity) async {
    try {
      final authModel = AuthModel.fromEntity(authEntity);
      await localDataSource.saveAuthData(authModel);
      return const Right(null);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, void>> clearAuthData() async {
    try {
      await localDataSource.clearAuthData();
      return const Right(null);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  Future<Either<Failure, void>> _simulateLocalForgotPassword(String email) async {
    try {
      // Simulate forgot password logic - in real app this would send email
      // For now, just return success
      return const Right(null);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  Future<Either<Failure, void>> _simulateLocalResetPassword(
    String email, String token, String newPassword) async {
    try {
      // Simulate reset password logic - in real app this would validate token and update password
      // For now, just return success
      return const Right(null);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  Failure _mapExceptionToFailure(dynamic exception) {
    if (exception is ServerException) {
      return ServerFailure(exception.message);
    } else if (exception is CacheException) {
      return CacheFailure(exception.message);
    } else if (exception is NetworkException) {
      return NetworkFailure(exception.message);
    } else if (exception is AuthException) {
      return AuthFailure(exception.message);
    } else {
      return GeneralFailure('An unexpected error occurred: $exception');
    }
  }
}
