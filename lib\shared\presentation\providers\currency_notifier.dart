/// Currency state management using Riverpod StateNotifier
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Currency state class
class CurrencyState {
  final String selectedCurrency;

  const CurrencyState({
    this.selectedCurrency = 'USD',
  });

  CurrencyState copyWith({
    String? selectedCurrency,
  }) {
    return CurrencyState(
      selectedCurrency: selectedCurrency ?? this.selectedCurrency,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CurrencyState &&
        other.selectedCurrency == selectedCurrency;
  }

  @override
  int get hashCode => selectedCurrency.hashCode;
}

/// Currency StateNotifier
class CurrencyNotifier extends StateNotifier<CurrencyState> {
  CurrencyNotifier() : super(const CurrencyState());

  /// Set selected currency
  void setCurrency(String currency) {
    state = state.copyWith(selectedCurrency: currency);
  }
}

/// Currency provider
final currencyProvider = StateNotifierProvider<CurrencyNotifier, CurrencyState>(
  (ref) => CurrencyNotifier(),
);
