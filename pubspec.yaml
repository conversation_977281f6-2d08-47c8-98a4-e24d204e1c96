name: kind_ali
description: "A new Flutter project."
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.6.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.8
  intl: ^0.19.0
  flutter_map: ^6.1.0
  latlong2: ^0.9.0
  url_launcher: ^6.2.5
  geocoding: ^2.2.0
  geolocator: ^10.1.0
  flutter_localization: ^0.3.2
  shared_preferences: ^2.5.3
  flutter_svg: ^2.1.0
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  dartz: ^0.10.1
  equatable: ^2.0.5
  get_it: ^7.6.4
  connectivity_plus: ^5.0.2
  http: ^1.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.1
  riverpod_generator: ^2.3.9
  build_runner: ^2.4.7
  json_annotation: ^4.8.1

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/png/
    - assets/json/
    - assets/language/
    - assets/svg/

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/applogo.jpg"
  min_sdk_android: 21
  remove_alpha_ios: true
  web:
    generate: true
    image_path: "assets/images/applogo.jpg"
  windows:
    generate: true
    image_path: "assets/images/applogo.jpg"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/images/applogo.jpg"
