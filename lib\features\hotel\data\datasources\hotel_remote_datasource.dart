import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:kind_ali/core/constants/api_constants.dart';
import 'package:kind_ali/core/error/exceptions.dart';
import '../models/hotel_details.dart';

/// Abstract class for hotel remote data source
abstract class HotelRemoteDataSource {
  /// Get hotels from remote API
  Future<List<InventoryInfoList>> getHotels();
  
  /// Get hotel details by ID from remote API
  Future<InventoryInfoList> getHotelDetails(int hotelId);
  
  /// Search hotels from remote API
  Future<List<InventoryInfoList>> searchHotels({
    required String destination,
    required DateTime checkIn,
    required DateTime checkOut,
    required int guests,
    required int rooms,
  });
  
  /// Get nearby hotels from remote API
  Future<List<InventoryInfoList>> getNearbyHotels({
    required double latitude,
    required double longitude,
    double radiusKm = 10.0,
  });
}

/// Implementation of hotel remote data source
class HotelRemoteDataSourceImpl implements HotelRemoteDataSource {
  final http.Client client;
  
  HotelRemoteDataSourceImpl({required this.client});

  @override
  Future<List<InventoryInfoList>> getHotels() async {
    try {
      final response = await client.get(
        Uri.parse('${ApiConstants.fullBaseUrl}${ApiConstants.hotels}'),
        headers: {
          ApiConstants.contentType: ApiConstants.applicationJson,
        },
      ).timeout(const Duration(seconds: ApiConstants.connectionTimeout));

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final Hotel hotel = Hotel.fromJson(jsonData);
        return hotel.data?.result?.inventoryInfoList ?? [];
      } else {
        throw ServerException('Failed to fetch hotels: ${response.statusCode}');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw NetworkException('Network error occurred: $e');
    }
  }

  @override
  Future<InventoryInfoList> getHotelDetails(int hotelId) async {
    try {
      final response = await client.get(
        Uri.parse('${ApiConstants.fullBaseUrl}${ApiConstants.hotelDetails.replaceAll('{id}', hotelId.toString())}'),
        headers: {
          ApiConstants.contentType: ApiConstants.applicationJson,
        },
      ).timeout(const Duration(seconds: ApiConstants.connectionTimeout));

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return InventoryInfoList.fromJson(jsonData['data']);
      } else {
        throw ServerException('Failed to fetch hotel details: ${response.statusCode}');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw NetworkException('Network error occurred: $e');
    }
  }

  @override
  Future<List<InventoryInfoList>> searchHotels({
    required String destination,
    required DateTime checkIn,
    required DateTime checkOut,
    required int guests,
    required int rooms,
  }) async {
    try {
      final queryParams = {
        'destination': destination,
        'check_in': checkIn.toIso8601String().split('T')[0],
        'check_out': checkOut.toIso8601String().split('T')[0],
        'guests': guests.toString(),
        'rooms': rooms.toString(),
      };

      final uri = Uri.parse('${ApiConstants.fullBaseUrl}${ApiConstants.searchHotels}')
          .replace(queryParameters: queryParams);

      final response = await client.get(
        uri,
        headers: {
          ApiConstants.contentType: ApiConstants.applicationJson,
        },
      ).timeout(const Duration(seconds: ApiConstants.connectionTimeout));

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final Hotel hotel = Hotel.fromJson(jsonData);
        return hotel.data?.result?.inventoryInfoList ?? [];
      } else {
        throw ServerException('Failed to search hotels: ${response.statusCode}');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw NetworkException('Network error occurred: $e');
    }
  }

  @override
  Future<List<InventoryInfoList>> getNearbyHotels({
    required double latitude,
    required double longitude,
    double radiusKm = 10.0,
  }) async {
    try {
      final queryParams = {
        'latitude': latitude.toString(),
        'longitude': longitude.toString(),
        'radius': radiusKm.toString(),
      };

      final uri = Uri.parse('${ApiConstants.fullBaseUrl}${ApiConstants.nearbyHotels}')
          .replace(queryParameters: queryParams);

      final response = await client.get(
        uri,
        headers: {
          ApiConstants.contentType: ApiConstants.applicationJson,
        },
      ).timeout(const Duration(seconds: ApiConstants.connectionTimeout));

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final Hotel hotel = Hotel.fromJson(jsonData);
        return hotel.data?.result?.inventoryInfoList ?? [];
      } else {
        throw ServerException('Failed to fetch nearby hotels: ${response.statusCode}');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw NetworkException('Network error occurred: $e');
    }
  }
}
