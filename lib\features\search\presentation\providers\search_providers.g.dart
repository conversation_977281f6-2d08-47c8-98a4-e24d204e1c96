// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$searchLocalDataSourceHash() =>
    r'a4432ca9ef95a2d00cd57ff0418294b1a334166d';

/// See also [searchLocalDataSource].
@ProviderFor(searchLocalDataSource)
final searchLocalDataSourceProvider =
    AutoDisposeFutureProvider<SearchLocalDataSource>.internal(
  searchLocalDataSource,
  name: r'searchLocalDataSourceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$searchLocalDataSourceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SearchLocalDataSourceRef
    = AutoDisposeFutureProviderRef<SearchLocalDataSource>;
String _$searchRepositoryHash() => r'a553bf82c9677ea1853f7c4b93bfdc6a228c045b';

/// See also [searchRepository].
@ProviderFor(searchRepository)
final searchRepositoryProvider =
    AutoDisposeFutureProvider<SearchRepository>.internal(
  searchRepository,
  name: r'searchRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$searchRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SearchRepositoryRef = AutoDisposeFutureProviderRef<SearchRepository>;
String _$getPopularDestinationsHash() =>
    r'111e84a98a728e9fb264675052aba1d09ff1e32b';

/// See also [getPopularDestinations].
@ProviderFor(getPopularDestinations)
final getPopularDestinationsProvider =
    AutoDisposeFutureProvider<GetPopularDestinations>.internal(
  getPopularDestinations,
  name: r'getPopularDestinationsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getPopularDestinationsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetPopularDestinationsRef
    = AutoDisposeFutureProviderRef<GetPopularDestinations>;
String _$getRecentSearchesHash() => r'a07c8f3881c6fb6d91334113134061993a6a7367';

/// See also [getRecentSearches].
@ProviderFor(getRecentSearches)
final getRecentSearchesProvider =
    AutoDisposeFutureProvider<GetRecentSearches>.internal(
  getRecentSearches,
  name: r'getRecentSearchesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getRecentSearchesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetRecentSearchesRef = AutoDisposeFutureProviderRef<GetRecentSearches>;
String _$addToRecentSearchesHash() =>
    r'6ae368f93813ea03b0df05d95a7e6068214e4b6a';

/// See also [addToRecentSearches].
@ProviderFor(addToRecentSearches)
final addToRecentSearchesProvider =
    AutoDisposeFutureProvider<AddToRecentSearches>.internal(
  addToRecentSearches,
  name: r'addToRecentSearchesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$addToRecentSearchesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AddToRecentSearchesRef
    = AutoDisposeFutureProviderRef<AddToRecentSearches>;
String _$getCitiesHash() => r'6ceb09ca65adc2e485846ce02402b37b7f8e1085';

/// See also [getCities].
@ProviderFor(getCities)
final getCitiesProvider = AutoDisposeFutureProvider<GetCities>.internal(
  getCities,
  name: r'getCitiesProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$getCitiesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetCitiesRef = AutoDisposeFutureProviderRef<GetCities>;
String _$searchCitiesHash() => r'88646843458d08d994aa127a62b7c855552c508d';

/// See also [searchCities].
@ProviderFor(searchCities)
final searchCitiesProvider = AutoDisposeFutureProvider<SearchCities>.internal(
  searchCities,
  name: r'searchCitiesProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$searchCitiesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SearchCitiesRef = AutoDisposeFutureProviderRef<SearchCities>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
