import 'package:equatable/equatable.dart';

/// Room entity representing the business model for hotel rooms
class RoomEntity extends Equatable {
  final String? roomId;
  final String? roomType;
  final String? roomName;
  final String? description;
  final double? price;
  final String? currency;
  final int? maxOccupancy;
  final int? availableRooms;
  final List<String>? amenities;
  final List<String>? imageUrls;
  final RoomSizeEntity? roomSize;
  final List<RoomOptionEntity>? roomOptions;

  const RoomEntity({
    this.roomId,
    this.roomType,
    this.roomName,
    this.description,
    this.price,
    this.currency,
    this.maxOccupancy,
    this.availableRooms,
    this.amenities,
    this.imageUrls,
    this.roomSize,
    this.roomOptions,
  });

  @override
  List<Object?> get props => [
        roomId,
        roomType,
        roomName,
        description,
        price,
        currency,
        maxOccupancy,
        availableRooms,
        amenities,
        imageUrls,
        roomSize,
        roomOptions,
      ];
}

/// Room size entity
class RoomSizeEntity extends Equatable {
  final double? area;
  final String? unit;

  const RoomSizeEntity({
    this.area,
    this.unit,
  });

  @override
  List<Object?> get props => [area, unit];
}

/// Room option entity (different pricing/meal plans)
class RoomOptionEntity extends Equatable {
  final String? optionId;
  final String? optionName;
  final String? mealPlan;
  final double? price;
  final String? currency;
  final bool? isRefundable;
  final bool? isBreakfastIncluded;
  final String? cancellationPolicy;
  final List<String>? inclusions;

  const RoomOptionEntity({
    this.optionId,
    this.optionName,
    this.mealPlan,
    this.price,
    this.currency,
    this.isRefundable,
    this.isBreakfastIncluded,
    this.cancellationPolicy,
    this.inclusions,
  });

  @override
  List<Object?> get props => [
        optionId,
        optionName,
        mealPlan,
        price,
        currency,
        isRefundable,
        isBreakfastIncluded,
        cancellationPolicy,
        inclusions,
      ];
}
