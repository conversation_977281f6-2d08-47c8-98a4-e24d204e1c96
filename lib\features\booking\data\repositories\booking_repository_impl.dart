﻿import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import 'package:kind_ali/core/error/failures.dart';
import 'package:kind_ali/core/network/network_info.dart';
import 'package:kind_ali/core/utils/enums.dart';
import '../../domain/entities/booking_entity.dart';
import '../../domain/repositories/booking_repository.dart';
import '../datasources/booking_local_datasource.dart';
import '../models/booking_model.dart';

/// Implementation of booking repository
class BookingRepositoryImpl implements BookingRepository {
  final BookingLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  BookingRepositoryImpl({
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, BookingEntity>> createBooking({
    required String hotelId,
    required String roomId,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    required int numberOfGuests,
    required int numberOfRooms,
    required GuestDetailsEntity guestDetails,
    String? specialRequests,
  }) async {
    try {
      // Generate a unique booking ID
      final bookingId = 'BK${DateTime.now().millisecondsSinceEpoch}';

      // Create booking entity from parameters
      final booking = BookingEntity(
        bookingId: bookingId,
        hotelId: hotelId,
        roomId: roomId,
        checkInDate: checkInDate,
        checkOutDate: checkOutDate,
        numberOfGuests: numberOfGuests,
        numberOfRooms: numberOfRooms,
        guestDetails: guestDetails,
        specialRequests: specialRequests,
        bookingDate: DateTime.now(),
        status: BookingStatus.pending,
      );

      final bookingModel = BookingModel.fromEntity(booking);

      await localDataSource.saveBooking(bookingModel);
      return Right(bookingModel.toEntity());
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<BookingEntity>>> getBookingHistory() async {
    try {
      final bookings = await localDataSource.getAllBookings();
      // Sort by booking date (newest first)
      bookings.sort((a, b) => 
        (b.bookingDate ?? DateTime.now()).compareTo(a.bookingDate ?? DateTime.now()));
      return Right(_mapBookingsToEntities(bookings));
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, BookingEntity>> getBookingDetails(String bookingId) async {
    try {
      final booking = await localDataSource.getBookingById(bookingId);
      if (booking == null) {
        return Left(CacheFailure('Booking not found'));
      }
      return Right(booking.toEntity());
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, BookingEntity>> updateBooking({
    required String bookingId,
    GuestDetailsEntity? guestDetails,
    String? specialRequests,
  }) async {
    try {
      // Get existing booking first
      final existingBooking = await localDataSource.getBookingById(bookingId);
      if (existingBooking == null) {
        return Left(CacheFailure('Booking not found'));
      }

      // Update only the provided fields
      final updatedBooking = existingBooking.copyWith(
        guestDetails: guestDetails != null ? GuestDetailsModel.fromEntity(guestDetails) : null,
        specialRequests: specialRequests,
      );

      await localDataSource.updateBooking(updatedBooking);
      return Right(updatedBooking.toEntity());
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, void>> cancelBooking(String bookingId) async {
    try {
      final booking = await localDataSource.getBookingById(bookingId);
      if (booking == null) {
        return const Left(CacheFailure('Booking not found'));
      }

      final updatedBooking = booking.copyWith(
        status: BookingStatus.cancelled,
        cancellationDate: DateTime.now(),
      );

      await localDataSource.updateBooking(updatedBooking);
      return const Right(null);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }



  @override
  Future<Either<Failure, List<BookingEntity>>> getUpcomingBookings() async {
    try {
      final allBookings = await localDataSource.getAllBookings();
      final now = DateTime.now();
      
      final upcomingBookings = allBookings.where((booking) {
        final checkInDate = booking.checkInDate;
        return checkInDate != null && 
               checkInDate.isAfter(now) && 
               booking.status != BookingStatus.cancelled;
      }).toList();
      
      // Sort by check-in date (earliest first)
      upcomingBookings.sort((a, b) => 
        (a.checkInDate ?? DateTime.now()).compareTo(b.checkInDate ?? DateTime.now()));
      
      return Right(_mapBookingsToEntities(upcomingBookings));
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<BookingEntity>>> getPastBookings() async {
    try {
      final allBookings = await localDataSource.getAllBookings();
      final now = DateTime.now();
      
      final pastBookings = allBookings.where((booking) {
        final checkOutDate = booking.checkOutDate;
        return checkOutDate != null && checkOutDate.isBefore(now);
      }).toList();
      
      // Sort by check-out date (most recent first)
      pastBookings.sort((a, b) => 
        (b.checkOutDate ?? DateTime.now()).compareTo(a.checkOutDate ?? DateTime.now()));
      
      return Right(_mapBookingsToEntities(pastBookings));
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, bool>> checkAvailability({
    required String hotelId,
    required String roomId,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    required int numberOfRooms,
  }) async {
    try {
      // For local implementation, we'll simulate availability checking
      // In a real scenario, this would check against hotel inventory

      final allBookings = await localDataSource.getAllBookings();

      // Check if there are conflicting bookings for the same hotel and room
      final conflictingBookings = allBookings.where((booking) {
        if (booking.hotelId != hotelId ||
            booking.roomId != roomId ||
            booking.status == BookingStatus.cancelled) {
          return false;
        }

        final bookingCheckIn = booking.checkInDate;
        final bookingCheckOut = booking.checkOutDate;

        if (bookingCheckIn == null || bookingCheckOut == null) {
          return false;
        }

        // Check for date overlap
        return !(checkOutDate.isBefore(bookingCheckIn) ||
                 checkInDate.isAfter(bookingCheckOut));
      }).toList();

      // Simple availability logic: assume each room type has 5 units max
      const maxRoomsPerType = 5;
      final bookedRooms = conflictingBookings.fold<int>(
        0,
        (sum, booking) => sum + (booking.numberOfRooms ?? 1),
      );

      final availableRooms = maxRoomsPerType - bookedRooms;
      return Right(availableRooms >= numberOfRooms);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<BookingEntity>>> getCancelledBookings() async {
    try {
      final allBookings = await localDataSource.getAllBookings();
      final cancelledBookings = allBookings
          .where((booking) => booking.status == BookingStatus.cancelled)
          .toList();

      // Sort by booking date (most recent first)
      cancelledBookings.sort((a, b) =>
        (b.bookingDate ?? DateTime.now()).compareTo(a.bookingDate ?? DateTime.now()));

      return Right(_mapBookingsToEntities(cancelledBookings));
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, double>> calculateBookingPrice({
    required String hotelId,
    required String roomId,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    required int numberOfRooms,
  }) async {
    try {
      // For local implementation, we'll simulate price calculation
      // In a real scenario, this would fetch room rates from hotel data

      final nights = checkOutDate.difference(checkInDate).inDays;
      if (nights <= 0) {
        return Left(GeneralFailure('Invalid date range'));
      }

      // Simple pricing logic - base rate per room per night
      const baseRatePerNight = 100.0; // $100 per night base rate

      // Apply some basic pricing rules
      double totalPrice = baseRatePerNight * nights * numberOfRooms;

      // Weekend surcharge (Friday and Saturday nights)
      int weekendNights = 0;
      for (int i = 0; i < nights; i++) {
        final currentDate = checkInDate.add(Duration(days: i));
        if (currentDate.weekday == DateTime.friday || currentDate.weekday == DateTime.saturday) {
          weekendNights++;
        }
      }
      totalPrice += weekendNights * numberOfRooms * 25.0; // $25 weekend surcharge per room

      // Multiple room discount (5% for 2+ rooms)
      if (numberOfRooms >= 2) {
        totalPrice *= 0.95;
      }

      return Right(totalPrice);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  // Helper methods
  List<BookingEntity> _mapBookingsToEntities(List<BookingModel> bookings) {
    return bookings.map((booking) => booking.toEntity()).toList();
  }

  Failure _mapExceptionToFailure(dynamic exception) {
    if (exception is ServerException) {
      return ServerFailure(exception.message);
    } else if (exception is CacheException) {
      return CacheFailure(exception.message);
    } else if (exception is NetworkException) {
      return NetworkFailure(exception.message);
    } else {
      return GeneralFailure('An unexpected error occurred: $exception');
    }
  }
}
