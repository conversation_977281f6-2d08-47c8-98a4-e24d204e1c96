﻿import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import 'package:kind_ali/core/error/failures.dart';
import 'package:kind_ali/core/network/network_info.dart';
import '../../domain/entities/search_entity.dart';
import '../../domain/repositories/search_repository.dart';
import '../datasources/search_local_datasource.dart';
import 'package:kind_ali/features/search/data/models/search_cities.dart';

/// Implementation of search repository
class SearchRepositoryImpl implements SearchRepository {
  final SearchLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  SearchRepositoryImpl({
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<CityEntity>>> getCities() async {
    try {
      // Try to get from cache first
      if (await localDataSource.isCitiesCacheValid()) {
        final cachedCities = await localDataSource.getCachedCities();
        return Right(_mapCitiesToEntities(cachedCities));
      } else {
        // Cache invalid or doesn't exist, get from assets
        final assetCities = await localDataSource.getCitiesFromAssets();
        // Cache the data for future use
        await localDataSource.cacheCities(assetCities);
        return Right(_mapCitiesToEntities(assetCities));
      }
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<CityEntity>>> searchCities(String query) async {
    try {
      final citiesResult = await getCities();
      return citiesResult.fold(
        (failure) => Left(failure),
        (cities) {
          if (query.isEmpty) {
            return Right(cities);
          }
          
          final filteredCities = cities.where((city) {
            final cityName = city.cityName?.toLowerCase() ?? '';
            final countryName = city.countryName?.toLowerCase() ?? '';
            final searchQuery = query.toLowerCase();

            return cityName.contains(searchQuery) ||
                   countryName.contains(searchQuery);
          }).toList();
          
          return Right(filteredCities);
        },
      );
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<PopularPlaceEntity>>> getPopularDestinations() async {
    try {
      final citiesResult = await getCities();
      return citiesResult.fold(
        (failure) => Left(failure),
        (cities) {
          // Convert cities to popular places and sort by property count
          final popularPlaces = cities
              .where((city) => city.propertyCount != null && city.propertyCount! > 0)
              .map((city) => PopularPlaceEntity(
                    placeId: city.cityId,
                    placeName: city.cityName,
                    cityName: city.cityName,
                    countryName: city.countryName,
                    imageUrl: null, // No image URL available in city data
                    description: '${city.cityName}, ${city.countryName}',
                    hotelCount: city.propertyCount,
                  ))
              .toList()
            ..sort((a, b) => (b.hotelCount ?? 0).compareTo(a.hotelCount ?? 0));

          final topDestinations = popularPlaces.take(10).toList();
          return Right(topDestinations);
        },
      );
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }



  @override
  Future<Either<Failure, List<String>>> getRecentSearches() async {
    try {
      final recentSearches = await localDataSource.getRecentSearches();
      return Right(recentSearches);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, void>> addToRecentSearches(String searchQuery) async {
    try {
      await localDataSource.addToRecentSearches(searchQuery);
      return const Right(null);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, void>> clearRecentSearches() async {
    try {
      await localDataSource.clearRecentSearches();
      return const Right(null);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }



  // Helper methods
  @override
  Future<Either<Failure, String>> getCurrentLocation() async {
    try {
      // For now, return a default location since we don't have location services
      // In a real app, this would use location services
      return const Right('Current Location');
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<CityEntity>>> getNearbyCities({
    required double latitude,
    required double longitude,
    double radiusKm = 50.0,
  }) async {
    try {
      // For now, return popular cities as nearby cities
      // In a real app, this would calculate distance and filter
      final cities = await localDataSource.getCitiesFromAssets();
      final limitedCities = cities.take(5).toList(); // Return first 5 as "nearby"
      return Right(_mapCitiesToEntities(limitedCities));
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<CityEntity>>> getPopularCities() async {
    try {
      // Get cities and return the most popular ones (those with highest property count)
      final cities = await localDataSource.getCitiesFromAssets();
      final sortedCities = cities.where((city) => city.propertyCount != null).toList()
        ..sort((a, b) => (b.propertyCount ?? 0).compareTo(a.propertyCount ?? 0));
      final popularCities = sortedCities.take(10).toList();
      return Right(_mapCitiesToEntities(popularCities));
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  List<CityEntity> _mapCitiesToEntities(List<SearchCities> cities) {
    return cities.map((city) => city.toEntity()).toList();
  }

  Failure _mapExceptionToFailure(dynamic exception) {
    if (exception is ServerException) {
      return ServerFailure(exception.message);
    } else if (exception is CacheException) {
      return CacheFailure(exception.message);
    } else if (exception is NetworkException) {
      return NetworkFailure(exception.message);
    } else if (exception is FileSystemException) {
      return CacheFailure(exception.message);
    } else {
      return GeneralFailure('An unexpected error occurred: $exception');
    }
  }
}
