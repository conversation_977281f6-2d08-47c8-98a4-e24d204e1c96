# PowerShell script to fix string extension imports
$filesToFix = @(
    "lib\features\authentication\presentation\pages\authentication\login_bottom_sheet.dart",
    "lib\features\booking\presentation\pages\booking\booking_screen.dart",
    "lib\features\booking\presentation\pages\booking\widgets\special_request_widget.dart",
    "lib\features\booking\presentation\pages\payment\payment_screen.dart",
    "lib\features\booking\presentation\pages\room_selection\booking_rooms_screen.dart",
    "lib\features\booking\presentation\pages\room_selection\widget\roomcard_widget.dart",
    "lib\features\booking\presentation\pages\room_selection\widget\room_options_widget.dart",
    "lib\features\home\presentation\pages\dashboard\dashboard_screen.dart",
    "lib\features\home\presentation\pages\home\widgets\guest_selection_widget.dart",
    "lib\features\hotel\presentation\pages\hotel_detail\widgets\hotel_map_widget.dart",
    "lib\features\profile\presentation\pages\language\language_screen.dart",
    "lib\features\profile\presentation\pages\profile\profile_completion_screen.dart",
    "lib\features\profile\presentation\pages\profile\profile_screen.dart",
    "lib\features\profile\presentation\pages\profile\widgets\currency_bottomsheet.dart",
    "lib\features\profile\presentation\pages\profile\widgets\personal_information_widget.dart",
    "lib\features\wishlist\presentation\pages\wishlist\wishlist_screen.dart",
    "lib\providers\home_provider.dart"
)

$updatedFiles = 0
$totalFiles = $filesToFix.Count

Write-Host "Processing $totalFiles Dart files with string extension import issues..."

foreach ($filePath in $filesToFix) {
    if (Test-Path $filePath) {
        $content = Get-Content $filePath -Raw
        $originalContent = $content
        
        # Fix string extension import paths
        $content = $content -replace "import 'package:kind_ali/core/extensions/string_extensions\.dart';", "import 'package:kind_ali/core/utils/string_extention_helper.dart';"
        $content = $content -replace "import 'package:kind_ali/core/extensions/string_extensions\.dart'", "import 'package:kind_ali/core/utils/string_extention_helper.dart'"
        
        if ($content -ne $originalContent) {
            Set-Content -Path $filePath -Value $content -NoNewline
            $updatedFiles++
            Write-Host "Updated: $filePath"
        }
    } else {
        Write-Host "File not found: $filePath"
    }
}

Write-Host "Script completed. Updated $updatedFiles out of $totalFiles files."
