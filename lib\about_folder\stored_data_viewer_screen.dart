import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';
import 'package:kind_ali/features/profile/data/models/profile_completion_model.dart';
import 'package:kind_ali/features/profile/data/services/profile_completion_service.dart';
import 'package:kind_ali/shared/presentation/widgets/custombutton_widget.dart';

class StoredDataViewerScreen extends ConsumerStatefulWidget {
  const StoredDataViewerScreen({super.key});

  @override
  ConsumerState<StoredDataViewerScreen> createState() => _StoredDataViewerScreenState();
}

class _StoredDataViewerScreenState extends ConsumerState<StoredDataViewerScreen> {
  ProfileCompletionModel? _profileData;
  ProfileCompletionStatus? _completionStatus;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadStoredData();
  }

  Future<void> _loadStoredData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final profileData = await ProfileCompletionService.getProfileData();
      final completionStatus = await ProfileCompletionService.getCompletionStatus();

      if (mounted) {
        setState(() {
          _profileData = profileData;
          _completionStatus = completionStatus;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _clearAllData() async {
    final confirmed = await _showConfirmationDialog();
    if (confirmed == true) {
      try {
        await ProfileCompletionService.clearAllData();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All stored data cleared successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        _loadStoredData(); // Refresh the display
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error clearing data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<bool?> _showConfirmationDialog() {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text('Are you sure you want to clear all stored profile data? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Stored Data Viewer'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _loadStoredData,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh Data',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? _buildErrorView()
              : _buildDataView(),
      bottomNavigationBar: _isLoading || _error != null
          ? null
          : Container(
              padding: const EdgeInsets.all(16),
              child: CustombuttonWidget(
                text: 'Clear All Data',
                backgroundColor: Colors.red,
                textColor: Colors.white,
                borderRadius: 12,
                height: 50,
                isFullWidth: true,
                onPressed: _clearAllData,
              ),
            ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'Error Loading Data',
            style: AppTextStyles.headline2.copyWith(color: Colors.red),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error occurred',
            style: AppTextStyles.bodyText1.copyWith(color: AppColors.textLight),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          CustombuttonWidget(
            text: 'Retry',
            backgroundColor: AppColors.primary,
            textColor: Colors.white,
            borderRadius: 12,
            height: 40,
            width: 120,
            onPressed: _loadStoredData,
          ),
        ],
      ),
    );
  }

  Widget _buildDataView() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatusCard(),
          const SizedBox(height: 16),
          _buildProfileDataCard(),
          const SizedBox(height: 16),
          _buildDebugInfoCard(),
        ],
      ),
    );
  }

  Widget _buildStatusCard() {
    final statusColor = _getStatusColor(_completionStatus);
    final statusText = _getStatusText(_completionStatus);

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: statusColor),
                const SizedBox(width: 8),
                Text(
                  'Profile Status',
                  style: AppTextStyles.headline3.copyWith(color: AppColors.text),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: statusColor),
              ),
              child: Text(
                statusText,
                style: AppTextStyles.bodyText2.copyWith(
                  color: statusColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: 12),
            if (_profileData != null) ...[
              Text(
                'Completion: ${_profileData!.completionPercentage.toStringAsFixed(1)}%',
                style: AppTextStyles.bodyText1.copyWith(color: AppColors.text),
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: _profileData!.completionPercentage / 100,
                backgroundColor: AppColors.divider,
                valueColor: AlwaysStoppedAnimation<Color>(statusColor),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProfileDataCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person_outline, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'Profile Data',
                  style: AppTextStyles.headline3.copyWith(color: AppColors.text),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_profileData != null) ...[
              _buildDataRow('Full Name', _profileData!.fullName),
              _buildDataRow('Email', _profileData!.email),
              _buildDataRow('Phone', _profileData!.phone),
              _buildDataRow('Date of Birth', _profileData!.dateOfBirth),
              _buildDataRow('Gender', _profileData!.gender),
              _buildDataRow('Address', _profileData!.address),
              _buildDataRow('City', _profileData!.city),
              _buildDataRow('Country', _profileData!.country),
              _buildDataRow('Emergency Contact', _profileData!.emergencyContact),
              _buildDataRow('Preferences', _profileData!.preferences),
              _buildDataRow('Created At', _profileData!.createdAt?.toString()),
              _buildDataRow('Updated At', _profileData!.updatedAt?.toString()),
            ] else
              const Text(
                'No profile data found',
                style: TextStyle(
                  color: AppColors.textLight,
                  fontStyle: FontStyle.italic,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDebugInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.bug_report_outlined, color: AppColors.secondary),
                const SizedBox(width: 8),
                Text(
                  'Debug Information',
                  style: AppTextStyles.headline3.copyWith(color: AppColors.text),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_profileData != null) ...[
              _buildDataRow('Is New User', _profileData!.isNewUser.toString()),
              _buildDataRow('Has Minimum Data', _profileData!.hasMinimumRequiredData.toString()),
              _buildDataRow('Missing Required Fields', _profileData!.missingRequiredFields.join(', ')),
              _buildDataRow('Missing Optional Fields', _profileData!.missingOptionalFields.join(', ')),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDataRow(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: AppTextStyles.bodyText2.copyWith(
                color: AppColors.textLight,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value?.isNotEmpty == true ? value! : 'Not set',
              style: AppTextStyles.bodyText2.copyWith(
                color: value?.isNotEmpty == true ? AppColors.text : AppColors.textLight,
                fontStyle: value?.isNotEmpty == true ? FontStyle.normal : FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(ProfileCompletionStatus? status) {
    switch (status) {
      case ProfileCompletionStatus.completeProfile:
        return Colors.green;
      case ProfileCompletionStatus.incompleteProfile:
        return Colors.orange;
      case ProfileCompletionStatus.newUser:
        return Colors.blue;
      default:
        return AppColors.textLight;
    }
  }

  String _getStatusText(ProfileCompletionStatus? status) {
    switch (status) {
      case ProfileCompletionStatus.completeProfile:
        return 'Complete Profile';
      case ProfileCompletionStatus.incompleteProfile:
        return 'Incomplete Profile';
      case ProfileCompletionStatus.newUser:
        return 'New User';
      default:
        return 'Unknown Status';
    }
  }
}
