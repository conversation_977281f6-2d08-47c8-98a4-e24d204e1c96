import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';
import 'package:kind_ali/core/constants/app_images.dart';
import 'package:kind_ali/features/profile/presentation/providers/profile_completion_notifier.dart';
import 'package:kind_ali/features/profile/data/services/profile_completion_service.dart';
import 'package:kind_ali/shared/presentation/widgets/custombutton_widget.dart';
import 'package:kind_ali/presentation/routes/app_routes.dart';

class ProfileCompletionScreen extends ConsumerStatefulWidget {
  const ProfileCompletionScreen({super.key});

  @override
  ConsumerState<ProfileCompletionScreen> createState() => _ProfileCompletionScreenState();
}

class _ProfileCompletionScreenState extends ConsumerState<ProfileCompletionScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();

  // Add controllers for text fields
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _dateOfBirthController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _cityController = TextEditingController();
  final TextEditingController _countryController = TextEditingController();

  String? _selectedGender;
  bool _isLoading = false;
  List<String> _missingRequiredFields = [];
  List<String> _missingOptionalFields = [];

  @override
  void initState() {
    super.initState();
    // Load existing profile data after widget initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadExistingProfileData();
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _dateOfBirthController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _countryController.dispose();
    super.dispose();
  }
  
  // Load existing profile data if available
  Future<void> _loadExistingProfileData() async {
    if (!mounted) return;

    try {
      // Load profile completion data from SharedPreferences
      final profileCompletionNotifier = ref.read(profileCompletionProvider.notifier);
      await profileCompletionNotifier.refresh();

      final profileState = ref.read(profileCompletionProvider);
      final profileData = profileState.profileData;

      if (mounted) {
        setState(() {
          // Pre-fill existing data
          _nameController.text = profileData.fullName ?? '';
          _emailController.text = profileData.email ?? '';
          _phoneController.text = profileData.phone ?? '';
          _dateOfBirthController.text = profileData.dateOfBirth ?? '';
          _addressController.text = profileData.address ?? '';
          _cityController.text = profileData.city ?? '';
          _countryController.text = profileData.country ?? '';
          _selectedGender = profileData.gender;

          // Get missing fields to determine which fields to show
          _missingRequiredFields = profileData.missingRequiredFields;
          _missingOptionalFields = profileData.missingOptionalFields;
        });
      }
    } catch (e) {
      debugPrint('Error loading profile completion data: $e');
      // Handle error - show all fields if there's an issue loading data
      if (mounted) {
        setState(() {
          _missingRequiredFields = ['fullName', 'email', 'phone'];
          _missingOptionalFields = ['dateOfBirth', 'gender', 'address', 'city', 'country'];
        });
      }
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Save profile completion data using SharedPreferences
      final profileCompletionNotifier = ref.read(profileCompletionProvider.notifier);

      final success = await profileCompletionNotifier.saveProfileData(
        fullName: _nameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim(),
        dateOfBirth: _dateOfBirthController.text.trim(),
        gender: _selectedGender,
        address: _addressController.text.trim(),
        city: _cityController.text.trim(),
        country: _countryController.text.trim(),
      );

      if (success && mounted) {
        // Export data to console for development (can be copied to about_folder/shared_preferences_data.txt)
        await ProfileCompletionService.exportStoredDataAsJson();

        if (mounted) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profile completed successfully! Data exported to console.'),
              backgroundColor: Colors.green,
            ),
          );

          // Navigate to dashboard after successful profile completion
          Navigator.pushNamedAndRemoveUntil(
            context,
            AppRoutes.dashboard,
            (route) => false,
          );
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to save profile. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope( // Use PopScope instead of deprecated WillPopScope
      canPop: false,
      onPopInvoked: (didPop) async {
        if (didPop) return;

        final shouldPop = await _confirmExit();
        if (shouldPop == true && context.mounted) {
          Navigator.pop(context);
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          backgroundColor: AppColors.background,
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back, color: AppColors.primary),
            onPressed: () async {
              final shouldPop = await _confirmExit();
              if (shouldPop == true && mounted) {
                Navigator.pop(context);
              }
            },
          ),
        
          centerTitle: true,
        ),
        body: SafeArea(
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Header section with logo and title
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    child: Column(
                      children: [
                        // Logo container
                        Container(
                          padding: const EdgeInsets.all(5),
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.primary.withAlpha(30),
                                blurRadius: 10,
                                spreadRadius: 2,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Image.asset(fit: BoxFit.cover,
                            AppImages.logo,
                            height: 50,
                            width: 120,
                            color: AppColors.primary,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Complete Your Profile',
                          style: AppTextStyles.headline2.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Please fill in your details to continue',
                          style: AppTextStyles.bodyText2.copyWith(
                            color: AppColors.textLight,
                            fontSize: 16,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        // Progress indicator
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 40),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  _buildProgressDot(true),
                                  _buildProgressLine(true),
                                  _buildProgressDot(true),
                                  _buildProgressLine(false),
                                  _buildProgressDot(false),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Step 2 of 3',
                                style: AppTextStyles.caption.copyWith(
                                  color: AppColors.textLight,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 10),

                  // Form container
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(30),
                          blurRadius: 15,
                          spreadRadius: 2,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Build conditional form fields based on missing data
                        ..._buildConditionalFormFields(),
                        const SizedBox(height: 32),

                        // Submit button
                        CustombuttonWidget(
                          text: 'Complete Profile',
                          backgroundColor: AppColors.primary,
                          textColor: Colors.white,
                          borderRadius: 12,
                          height: 56,
                          isFullWidth: true,
                          isLoading: _isLoading,
                          onPressed: _isLoading ? () {} : _saveProfile,
                          textStyle: AppTextStyles.button.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Additional info text
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.divider,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: AppColors.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'Your information is secure and will only be used to enhance your booking experience.',
                            style: AppTextStyles.bodyText2.copyWith(
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Build progress dot for step indicator
  Widget _buildProgressDot(bool isCompleted) {
    return Container(
      width: 12,
      height: 12,
      decoration: BoxDecoration(
        color: isCompleted ? AppColors.primary : AppColors.divider,
        shape: BoxShape.circle,
      ),
    );
  }

  // Build progress line for step indicator
  Widget _buildProgressLine(bool isCompleted) {
    return Container(
      width: 30,
      height: 2,
      color: isCompleted ? AppColors.primary : AppColors.divider,
    );
  }

  // Build custom input field matching app design
  Widget _buildInputField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    required IconData prefixIcon,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.divider),
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        maxLines: maxLines,
        style: const TextStyle(
          fontSize: 16,
          color: AppColors.text,
        ),
        decoration: InputDecoration(
          labelText: labelText,
          hintText: hintText,
          labelStyle: const TextStyle(color: AppColors.textLight),
          hintStyle: TextStyle(color: AppColors.textLight.withAlpha(128)),
          prefixIcon: Icon(
            prefixIcon,
            color: AppColors.primary,
            size: 22,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            vertical: 16,
            horizontal: 20,
          ),
        ),
        validator: validator,
      ),
    );
  }

  // Build conditional form fields based on missing data
  List<Widget> _buildConditionalFormFields() {
    final List<Widget> fields = [];

    // Required fields
    if (_missingRequiredFields.contains('fullName')) {
      fields.add(_buildInputField(
        controller: _nameController,
        labelText: 'Full Name *',
        hintText: 'Enter your full name',
        prefixIcon: Icons.person_outline,
        validator: (value) {
          if (value == null || value.trim().isEmpty) {
            return 'Please enter your name';
          }
          return null;
        },
      ));
      fields.add(const SizedBox(height: 20));
    }

    if (_missingRequiredFields.contains('email')) {
      fields.add(_buildInputField(
        controller: _emailController,
        labelText: 'Email Address *',
        hintText: 'Enter your email address',
        prefixIcon: Icons.email_outlined,
        keyboardType: TextInputType.emailAddress,
        validator: (value) {
          if (value == null || value.trim().isEmpty) {
            return 'Please enter your email';
          }
          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
            return 'Please enter a valid email';
          }
          return null;
        },
      ));
      fields.add(const SizedBox(height: 20));
    }

    if (_missingRequiredFields.contains('phone')) {
      fields.add(_buildInputField(
        controller: _phoneController,
        labelText: 'Phone Number *',
        hintText: 'Enter your phone number',
        prefixIcon: Icons.phone_outlined,
        keyboardType: TextInputType.phone,
        validator: (value) {
          if (value == null || value.trim().isEmpty) {
            return 'Please enter your phone number';
          }
          return null;
        },
      ));
      fields.add(const SizedBox(height: 20));
    }

    // Optional fields
    if (_missingOptionalFields.contains('dateOfBirth')) {
      fields.add(_buildInputField(
        controller: _dateOfBirthController,
        labelText: 'Date of Birth',
        hintText: 'DD/MM/YYYY',
        prefixIcon: Icons.calendar_today_outlined,
        keyboardType: TextInputType.datetime,
      ));
      fields.add(const SizedBox(height: 20));
    }

    if (_missingOptionalFields.contains('gender')) {
      fields.add(_buildGenderField());
      fields.add(const SizedBox(height: 20));
    }

    if (_missingOptionalFields.contains('address')) {
      fields.add(_buildInputField(
        controller: _addressController,
        labelText: 'Address',
        hintText: 'Enter your address',
        prefixIcon: Icons.location_on_outlined,
        maxLines: 2,
      ));
      fields.add(const SizedBox(height: 20));
    }

    if (_missingOptionalFields.contains('city')) {
      fields.add(_buildInputField(
        controller: _cityController,
        labelText: 'City',
        hintText: 'Enter your city',
        prefixIcon: Icons.location_city_outlined,
      ));
      fields.add(const SizedBox(height: 20));
    }

    if (_missingOptionalFields.contains('country')) {
      fields.add(_buildInputField(
        controller: _countryController,
        labelText: 'Country',
        hintText: 'Enter your country',
        prefixIcon: Icons.public_outlined,
      ));
      fields.add(const SizedBox(height: 20));
    }

    // Remove the last SizedBox if fields exist
    if (fields.isNotEmpty && fields.last is SizedBox) {
      fields.removeLast();
    }

    return fields;
  }

  // Build gender selection field
  Widget _buildGenderField() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.divider),
        borderRadius: BorderRadius.circular(12),
      ),
      child: DropdownButtonFormField<String>(
        value: _selectedGender,
        decoration: const InputDecoration(
          labelText: 'Gender',
          hintText: 'Select your gender',
          labelStyle: TextStyle(color: AppColors.textLight),
          prefixIcon: Icon(
            Icons.person_outline,
            color: AppColors.primary,
            size: 22,
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            vertical: 16,
            horizontal: 20,
          ),
        ),
        items: const [
          DropdownMenuItem(value: 'Male', child: Text('Male')),
          DropdownMenuItem(value: 'Female', child: Text('Female')),
          DropdownMenuItem(value: 'Other', child: Text('Other')),
        ],
        onChanged: (value) {
          setState(() {
            _selectedGender = value;
          });
        },
      ),
    );
  }

  Future<bool?> _confirmExit() {
    if (!mounted) return Future.value(false);

    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'Discard Changes?',
          style: AppTextStyles.headline3.copyWith(
            color: AppColors.primary,
          ),
        ),
        content: Text(
          'If you go back now, your profile information will not be saved.',
          style: AppTextStyles.bodyText1.copyWith(
            color: AppColors.textLight,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'Cancel',
              style: TextStyle(
                color: AppColors.textLight,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          CustombuttonWidget(
            text: 'Discard',
            backgroundColor: AppColors.error,
            textColor: Colors.white,
            borderRadius: 8,
            height: 40,
            width: 80,
            onPressed: () => Navigator.pop(context, true),
            textStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}