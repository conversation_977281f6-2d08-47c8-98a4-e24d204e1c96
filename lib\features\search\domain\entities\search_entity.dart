import 'package:equatable/equatable.dart';

/// Search entity for hotel search operations
class HotelSearchEntity extends Equatable {
  final String? destination;
  final DateTime? checkInDate;
  final DateTime? checkOutDate;
  final int? numberOfGuests;
  final int? numberOfRooms;
  final List<int>? guestAges;
  final String? currency;
  final HotelSearchFiltersEntity? filters;

  const HotelSearchEntity({
    this.destination,
    this.checkInDate,
    this.checkOutDate,
    this.numberOfGuests,
    this.numberOfRooms,
    this.guestAges,
    this.currency,
    this.filters,
  });

  /// Calculate number of nights
  int get numberOfNights {
    if (checkInDate != null && checkOutDate != null) {
      return checkOutDate!.difference(checkInDate!).inDays;
    }
    return 0;
  }

  @override
  List<Object?> get props => [
        destination,
        checkInDate,
        checkOutDate,
        numberOfGuests,
        numberOfRooms,
        guestAges,
        currency,
        filters,
      ];
}

/// Hotel search filters entity
class HotelSearchFiltersEntity extends Equatable {
  final double? minPrice;
  final double? maxPrice;
  final double? minRating;
  final List<String>? amenities;
  final List<String>? hotelTypes;
  final String? sortBy; // price_asc, price_desc, rating, distance
  final double? maxDistanceKm;

  const HotelSearchFiltersEntity({
    this.minPrice,
    this.maxPrice,
    this.minRating,
    this.amenities,
    this.hotelTypes,
    this.sortBy,
    this.maxDistanceKm,
  });

  @override
  List<Object?> get props => [
        minPrice,
        maxPrice,
        minRating,
        amenities,
        hotelTypes,
        sortBy,
        maxDistanceKm,
      ];
}

/// City/destination entity
class CityEntity extends Equatable {
  final String? cityId;
  final String? cityName;
  final String? countryName;
  final String? countryCode;
  final int? propertyCount;
  final String? imageUrl;
  final bool? isPopular;
  final double? latitude;
  final double? longitude;

  const CityEntity({
    this.cityId,
    this.cityName,
    this.countryName,
    this.countryCode,
    this.propertyCount,
    this.imageUrl,
    this.isPopular,
    this.latitude,
    this.longitude,
  });

  @override
  List<Object?> get props => [
        cityId,
        cityName,
        countryName,
        countryCode,
        propertyCount,
        imageUrl,
        isPopular,
        latitude,
        longitude,
      ];
}

/// Popular place entity
class PopularPlaceEntity extends Equatable {
  final String? placeId;
  final String? placeName;
  final String? cityName;
  final String? countryName;
  final String? imageUrl;
  final String? description;
  final int? hotelCount;

  const PopularPlaceEntity({
    this.placeId,
    this.placeName,
    this.cityName,
    this.countryName,
    this.imageUrl,
    this.description,
    this.hotelCount,
  });

  @override
  List<Object?> get props => [
        placeId,
        placeName,
        cityName,
        countryName,
        imageUrl,
        description,
        hotelCount,
      ];
}
