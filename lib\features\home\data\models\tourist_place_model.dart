// To parse this JSON data, do
//
//     final touristPlaces = touristPlacesFromJson(jsonString);

import 'dart:convert';

List<TouristPlaces> touristPlacesFromJson(String str) => List<TouristPlaces>.from(json.decode(str).map((x) => TouristPlaces.fromJson(x)));

String touristPlacesToJson(List<TouristPlaces> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class TouristPlaces {
    String? country;
    List<String>? places;

    TouristPlaces({
        this.country,
        this.places,
    });

    factory TouristPlaces.fromJson(Map<String, dynamic> json) => TouristPlaces(
        country: json["country"],
        places: json["places"] == null ? [] : List<String>.from(json["places"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "country": country,
        "places": places == null ? [] : List<dynamic>.from(places!.map((x) => x)),
    };
}
