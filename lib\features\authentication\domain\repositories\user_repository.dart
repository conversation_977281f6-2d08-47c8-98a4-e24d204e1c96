﻿import 'package:dartz/dartz.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../entities/user_entity.dart';

/// Abstract repository for user-related operations
abstract class UserRepository {
  /// Get current user profile
  Future<Either<Failure, UserEntity>> getUserProfile();

  /// Update user profile
  Future<Either<Failure, UserEntity>> updateUserProfile({
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    DateTime? dateOfBirth,
    String? nationality,
    String? passportNumber,
    String? preferredLanguage,
    String? preferredCurrency,
  });

  /// Upload profile image
  Future<Either<Failure, String>> uploadProfileImage(String imagePath);

  /// Update profile image
  Future<Either<Failure, UserEntity>> updateProfileImage(String imageUrl);

  /// Verify email
  Future<Either<Failure, void>> verifyEmail(String verificationToken);

  /// Verify phone
  Future<Either<Failure, void>> verifyPhone({
    required String phone,
    required String otp,
  });

  /// Change password
  Future<Either<Failure, void>> changePassword({
    required String currentPassword,
    required String newPassword,
  });

  /// Delete user account
  Future<Either<Failure, void>> deleteAccount();

  /// Get user preferences
  Future<Either<Failure, UserPreferencesEntity>> getUserPreferences();

  /// Update user preferences
  Future<Either<Failure, UserPreferencesEntity>> updateUserPreferences({
    String? language,
    String? currency,
    bool? emailNotifications,
    bool? pushNotifications,
    bool? smsNotifications,
  });
}

/// User preferences entity
class UserPreferencesEntity {
  final String? language;
  final String? currency;
  final bool? emailNotifications;
  final bool? pushNotifications;
  final bool? smsNotifications;

  const UserPreferencesEntity({
    this.language,
    this.currency,
    this.emailNotifications,
    this.pushNotifications,
    this.smsNotifications,
  });
}
