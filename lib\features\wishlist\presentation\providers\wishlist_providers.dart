/// Wishlist providers for Riverpod dependency injection
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:kind_ali/features/hotel/domain/repositories/hotel_repository.dart';
import 'package:kind_ali/features/hotel/presentation/providers/hotel_providers.dart';
import '../../domain/usecases/wishlist/manage_wishlist.dart';

part 'wishlist_providers.g.dart';

// Use Cases
@riverpod
Future<GetWishlistHotels> getWishlistHotels(Ref ref) async {
  final repository = await ref.watch(hotelRepositoryProvider.future);
  return GetWishlistHotels(repository);
}

@riverpod
Future<AddToWishlist> addToWishlist(Ref ref) async {
  final repository = await ref.watch(hotelRepositoryProvider.future);
  return AddToWishlist(repository);
}

@riverpod
Future<RemoveFromWishlist> removeFromWishlist(Ref ref) async {
  final repository = await ref.watch(hotelRepositoryProvider.future);
  return RemoveFromWishlist(repository);
}
