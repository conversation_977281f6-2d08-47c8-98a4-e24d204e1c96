# PowerShell script to fix critical remaining import issues

# Define critical import fixes
$criticalFixes = @{
    # CRITICAL: Fix string extensions for .tr method
    'package:kind_ali/core/extensions/string_extensions.dart' = 'package:kind_ali/core/utils/string_extention_helper.dart'
    
    # Core error handling - CRITICAL FOR FAILURE TYPES
    '../../../core/error/failures.dart' = 'package:kind_ali/core/error/failures.dart'
    '../../../../core/error/failures.dart' = 'package:kind_ali/core/error/failures.dart'
    
    # Core network
    '../../../core/network/network_info.dart' = 'package:kind_ali/core/network/network_info.dart'
    
    # Base usecase - CRITICAL FOR USE CASES
    '../base/usecase.dart' = 'package:kind_ali/core/utils/typedef.dart'
    
    # Missing widget imports
    'package:kind_ali/widgets/ArcAvatarLoader_widget.dart' = 'package:kind_ali/shared/presentation/widgets/ArcAvatarLoader_widget.dart'
    'package:kind_ali/widgets/custombutton_widget.dart' = 'package:kind_ali/shared/presentation/widgets/custombutton_widget.dart'
    
    # Dashboard screen import
    'package:kind_ali/screens/dashboard screen/dashboard_screen.dart' = 'package:kind_ali/features/home/<USER>/pages/dashboard/dashboard_screen.dart'
    
    # Missing model imports - search cities
    '../models/search_cities.dart' = 'package:kind_ali/features/search/data/models/search_cities.dart'
    
    # Guest selection widget path fix
    '../../../features/home/<USER>/pages/home/<USER>/guest_selection_widget.dart' = 'package:kind_ali/features/home/<USER>/pages/home/<USER>/guest_selection_widget.dart'
}

# Get all Dart files in the project
$dartFiles = Get-ChildItem -Path "lib" -Recurse -Filter "*.dart"

$totalFiles = $dartFiles.Count
$processedFiles = 0
$updatedFiles = 0

Write-Host "🔧 CRITICAL IMPORT FIXES - Processing $totalFiles Dart files..." -ForegroundColor Yellow

foreach ($file in $dartFiles) {
    $processedFiles++
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    $fileUpdated = $false
    
    # Apply critical fixes
    foreach ($oldImport in $criticalFixes.Keys) {
        $newImport = $criticalFixes[$oldImport]
        
        # Fix import statements
        if ($content -match "import\s+['""]$([regex]::Escape($oldImport))['""]") {
            $content = $content -replace "import\s+['""]$([regex]::Escape($oldImport))['""]", "import '$newImport'"
            $fileUpdated = $true
        }
        
        # Fix export statements
        if ($content -match "export\s+['""]$([regex]::Escape($oldImport))['""]") {
            $content = $content -replace "export\s+['""]$([regex]::Escape($oldImport))['""]", "export '$newImport'"
            $fileUpdated = $true
        }
    }
    
    # Save if updated
    if ($fileUpdated) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        $updatedFiles++
        Write-Host "✅ Updated: $($file.Name)" -ForegroundColor Green
    }
    
    # Progress indicator
    if ($processedFiles % 20 -eq 0) {
        Write-Host "📊 Progress: $processedFiles/$totalFiles files processed, $updatedFiles updated" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "🎉 CRITICAL FIXES COMPLETE!" -ForegroundColor Green
Write-Host "📊 Total files processed: $totalFiles" -ForegroundColor White
Write-Host "✅ Files updated: $updatedFiles" -ForegroundColor Green
Write-Host ""
Write-Host "🔍 Run 'flutter analyze' to check remaining issues..." -ForegroundColor Yellow
