import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/profile_completion_model.dart';
import '../../data/services/profile_completion_service.dart';

/// State for profile completion
class ProfileCompletionState {
  final ProfileCompletionModel profileData;
  final bool isLoading;
  final String? error;
  final bool isNewUser;
  final ProfileCompletionStatus completionStatus;

  const ProfileCompletionState({
    required this.profileData,
    this.isLoading = false,
    this.error,
    this.isNewUser = true,
    this.completionStatus = ProfileCompletionStatus.newUser,
  });

  ProfileCompletionState copyWith({
    ProfileCompletionModel? profileData,
    bool? isLoading,
    String? error,
    bool? isNewUser,
    ProfileCompletionStatus? completionStatus,
  }) {
    return ProfileCompletionState(
      profileData: profileData ?? this.profileData,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isNewUser: isNewUser ?? this.isNewUser,
      completionStatus: completionStatus ?? this.completionStatus,
    );
  }
}

/// Notifier for profile completion
class ProfileCompletionNotifier extends StateNotifier<ProfileCompletionState> {
  ProfileCompletionNotifier() : super(ProfileCompletionState(
    profileData: ProfileCompletionModel.empty(),
  )) {
    _loadProfileData();
  }

  /// Load profile data from SharedPreferences
  Future<void> _loadProfileData() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final profileData = await ProfileCompletionService.getProfileData();
      final isNewUser = await ProfileCompletionService.isNewUser();
      final completionStatus = await ProfileCompletionService.getCompletionStatus();
      
      state = state.copyWith(
        profileData: profileData,
        isLoading: false,
        isNewUser: isNewUser,
        completionStatus: completionStatus,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load profile data: $e',
      );
    }
  }

  /// Save profile completion data
  Future<bool> saveProfileData({
    String? fullName,
    String? email,
    String? phone,
    String? dateOfBirth,
    String? gender,
    String? address,
    String? city,
    String? country,
    String? profileImagePath,
  }) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final updatedProfile = state.profileData.copyWith(
        fullName: fullName,
        email: email,
        phone: phone,
        dateOfBirth: dateOfBirth,
        gender: gender,
        address: address,
        city: city,
        country: country,
        profileImagePath: profileImagePath,
      );
      
      final success = await ProfileCompletionService.saveProfileData(updatedProfile);
      
      if (success) {
        // Reload data to get updated status
        await _loadProfileData();
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to save profile data',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Error saving profile data: $e',
      );
      return false;
    }
  }

  /// Update specific profile fields
  Future<bool> updateProfileFields({
    String? fullName,
    String? email,
    String? phone,
    String? dateOfBirth,
    String? gender,
    String? address,
    String? city,
    String? country,
    String? profileImagePath,
  }) async {
    return await saveProfileData(
      fullName: fullName,
      email: email,
      phone: phone,
      dateOfBirth: dateOfBirth,
      gender: gender,
      address: address,
      city: city,
      country: country,
      profileImagePath: profileImagePath,
    );
  }

  /// Check if profile completion is required
  Future<bool> isProfileCompletionRequired() async {
    try {
      return await ProfileCompletionService.isProfileCompletionRequired();
    } catch (e) {
      return true;
    }
  }

  /// Mark user as no longer new
  Future<void> markUserAsExisting() async {
    try {
      await ProfileCompletionService.markUserAsExisting();
      await _loadProfileData(); // Reload to update status
    } catch (e) {
      state = state.copyWith(error: 'Failed to update user status: $e');
    }
  }

  /// Get missing required fields
  List<String> getMissingRequiredFields() {
    return state.profileData.missingRequiredFields;
  }

  /// Get missing optional fields
  List<String> getMissingOptionalFields() {
    return state.profileData.missingOptionalFields;
  }

  /// Get completion percentage
  double getCompletionPercentage() {
    return state.profileData.completionPercentage;
  }

  /// Check if user is new
  bool isUserNew() {
    return state.isNewUser;
  }

  /// Check if profile has minimum required data
  bool hasMinimumRequiredData() {
    return state.profileData.hasMinimumRequiredData;
  }

  /// Clear all profile data
  Future<bool> clearProfileData() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final success = await ProfileCompletionService.clearProfileData();
      
      if (success) {
        await _loadProfileData(); // Reload to reset state
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to clear profile data',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Error clearing profile data: $e',
      );
      return false;
    }
  }

  /// Refresh profile data
  Future<void> refresh() async {
    await _loadProfileData();
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Get stored data as string for debugging
  Future<String> getStoredDataAsString() async {
    try {
      return await ProfileCompletionService.getStoredDataAsString();
    } catch (e) {
      return 'Error retrieving stored data: $e';
    }
  }

  /// Get all SharedPreferences data for debugging
  Future<Map<String, dynamic>> getAllSharedPreferencesData() async {
    try {
      return await ProfileCompletionService.getAllSharedPreferencesData();
    } catch (e) {
      return {'error': e.toString()};
    }
  }
}

/// Provider for profile completion
final profileCompletionProvider = StateNotifierProvider<ProfileCompletionNotifier, ProfileCompletionState>(
  (ref) => ProfileCompletionNotifier(),
);

/// Provider to check if profile completion is required
final profileCompletionRequiredProvider = FutureProvider<bool>((ref) async {
  return await ProfileCompletionService.isProfileCompletionRequired();
});

/// Provider to get completion status
final profileCompletionStatusProvider = FutureProvider<ProfileCompletionStatus>((ref) async {
  return await ProfileCompletionService.getCompletionStatus();
});

/// Provider to check if user is new
final isNewUserProvider = FutureProvider<bool>((ref) async {
  return await ProfileCompletionService.isNewUser();
});
