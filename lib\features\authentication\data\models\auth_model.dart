import 'package:kind_ali/core/utils/enums.dart';
import '../../domain/entities/user_entity.dart';
import 'user_model.dart';

/// Authentication model for data layer
class AuthModel extends AuthEntity {
  const AuthModel({
    super.accessToken,
    super.refreshToken,
    super.expiresAt,
    super.user,
    super.status,
  });

  /// Create AuthModel from JSON
  factory AuthModel.fromJson(Map<String, dynamic> json) {
    return AuthModel(
      accessToken: json['access_token'] as String?,
      refreshToken: json['refresh_token'] as String?,
      expiresAt: json['expires_at'] != null
          ? DateTime.parse(json['expires_at'] as String)
          : null,
      user: json['user'] != null
          ? UserModel.fromJson(json['user'] as Map<String, dynamic>)
          : null,
      status: json['status'] != null
          ? AuthStatus.values.firstWhere(
              (e) => e.toString().split('.').last == json['status'],
              orElse: () => AuthStatus.unauthenticated,
            )
          : null,
    );
  }

  /// Convert AuthModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'expires_at': expiresAt?.toIso8601String(),
      'user': user != null ? (user as UserModel).toJson() : null,
      'status': status?.toString().split('.').last,
    };
  }

  /// Create AuthModel from AuthEntity
  factory AuthModel.fromEntity(AuthEntity entity) {
    return AuthModel(
      accessToken: entity.accessToken,
      refreshToken: entity.refreshToken,
      expiresAt: entity.expiresAt,
      user: entity.user != null ? UserModel.fromEntity(entity.user!) : null,
      status: entity.status,
    );
  }

  /// Convert to AuthEntity
  AuthEntity toEntity() {
    return AuthEntity(
      accessToken: accessToken,
      refreshToken: refreshToken,
      expiresAt: expiresAt,
      user: user, // user is already a UserEntity, no need to convert
      status: status,
    );
  }

  /// Check if token is still valid
  bool isTokenValid() {
    if (accessToken == null) return false;
    if (expiresAt == null) return true; // No expiry set, assume valid
    return DateTime.now().isBefore(expiresAt!);
  }

  /// Copy with method
  AuthModel copyWith({
    String? accessToken,
    String? refreshToken,
    DateTime? expiresAt,
    UserModel? user,
    AuthStatus? status,
  }) {
    return AuthModel(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      expiresAt: expiresAt ?? this.expiresAt,
      user: user ?? this.user as UserModel?,
      status: status ?? this.status,
    );
  }
}
