import 'dart:convert';

/// Model for profile completion data stored in SharedPreferences
class ProfileCompletionModel {
  final String? fullName;
  final String? email;
  final String? phone;
  final String? dateOfBirth;
  final String? gender;
  final String? address;
  final String? city;
  final String? country;
  final String? profileImagePath;
  final bool isProfileComplete;
  final DateTime? lastUpdated;

  const ProfileCompletionModel({
    this.fullName,
    this.email,
    this.phone,
    this.dateOfBirth,
    this.gender,
    this.address,
    this.city,
    this.country,
    this.profileImagePath,
    this.isProfileComplete = false,
    this.lastUpdated,
  });

  /// Create empty profile
  factory ProfileCompletionModel.empty() {
    return const ProfileCompletionModel();
  }

  /// Create from JSON
  factory ProfileCompletionModel.fromJson(Map<String, dynamic> json) {
    return ProfileCompletionModel(
      fullName: json['fullName'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      dateOfBirth: json['dateOfBirth'] as String?,
      gender: json['gender'] as String?,
      address: json['address'] as String?,
      city: json['city'] as String?,
      country: json['country'] as String?,
      profileImagePath: json['profileImagePath'] as String?,
      isProfileComplete: json['isProfileComplete'] as bool? ?? false,
      lastUpdated: json['lastUpdated'] != null 
          ? DateTime.parse(json['lastUpdated'] as String)
          : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'fullName': fullName,
      'email': email,
      'phone': phone,
      'dateOfBirth': dateOfBirth,
      'gender': gender,
      'address': address,
      'city': city,
      'country': country,
      'profileImagePath': profileImagePath,
      'isProfileComplete': isProfileComplete,
      'lastUpdated': lastUpdated?.toIso8601String(),
    };
  }

  /// Create copy with updated fields
  ProfileCompletionModel copyWith({
    String? fullName,
    String? email,
    String? phone,
    String? dateOfBirth,
    String? gender,
    String? address,
    String? city,
    String? country,
    String? profileImagePath,
    bool? isProfileComplete,
    DateTime? lastUpdated,
  }) {
    return ProfileCompletionModel(
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      profileImagePath: profileImagePath ?? this.profileImagePath,
      isProfileComplete: isProfileComplete ?? this.isProfileComplete,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Check if user is new (no profile data exists)
  bool get isNewUser {
    return fullName == null && 
           email == null && 
           phone == null && 
           dateOfBirth == null && 
           gender == null && 
           address == null && 
           city == null && 
           country == null;
  }

  /// Get list of missing required fields
  List<String> get missingRequiredFields {
    final missing = <String>[];
    
    if (fullName == null || fullName!.trim().isEmpty) {
      missing.add('fullName');
    }
    if (email == null || email!.trim().isEmpty) {
      missing.add('email');
    }
    if (phone == null || phone!.trim().isEmpty) {
      missing.add('phone');
    }
    
    return missing;
  }

  /// Get list of missing optional fields
  List<String> get missingOptionalFields {
    final missing = <String>[];
    
    if (dateOfBirth == null || dateOfBirth!.trim().isEmpty) {
      missing.add('dateOfBirth');
    }
    if (gender == null || gender!.trim().isEmpty) {
      missing.add('gender');
    }
    if (address == null || address!.trim().isEmpty) {
      missing.add('address');
    }
    if (city == null || city!.trim().isEmpty) {
      missing.add('city');
    }
    if (country == null || country!.trim().isEmpty) {
      missing.add('country');
    }
    
    return missing;
  }

  /// Check if profile has minimum required data
  bool get hasMinimumRequiredData {
    return missingRequiredFields.isEmpty;
  }

  /// Check if profile is completely filled
  bool get isCompletelyFilled {
    return missingRequiredFields.isEmpty && missingOptionalFields.isEmpty;
  }

  /// Get completion percentage
  double get completionPercentage {
    const totalFields = 8; // fullName, email, phone, dateOfBirth, gender, address, city, country
    int filledFields = 0;
    
    if (fullName != null && fullName!.trim().isNotEmpty) filledFields++;
    if (email != null && email!.trim().isNotEmpty) filledFields++;
    if (phone != null && phone!.trim().isNotEmpty) filledFields++;
    if (dateOfBirth != null && dateOfBirth!.trim().isNotEmpty) filledFields++;
    if (gender != null && gender!.trim().isNotEmpty) filledFields++;
    if (address != null && address!.trim().isNotEmpty) filledFields++;
    if (city != null && city!.trim().isNotEmpty) filledFields++;
    if (country != null && country!.trim().isNotEmpty) filledFields++;
    
    return (filledFields / totalFields) * 100;
  }

  @override
  String toString() {
    return 'ProfileCompletionModel(fullName: $fullName, email: $email, phone: $phone, '
           'dateOfBirth: $dateOfBirth, gender: $gender, address: $address, '
           'city: $city, country: $country, isProfileComplete: $isProfileComplete, '
           'completionPercentage: ${completionPercentage.toStringAsFixed(1)}%)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is ProfileCompletionModel &&
        other.fullName == fullName &&
        other.email == email &&
        other.phone == phone &&
        other.dateOfBirth == dateOfBirth &&
        other.gender == gender &&
        other.address == address &&
        other.city == city &&
        other.country == country &&
        other.profileImagePath == profileImagePath &&
        other.isProfileComplete == isProfileComplete &&
        other.lastUpdated == lastUpdated;
  }

  @override
  int get hashCode {
    return Object.hash(
      fullName,
      email,
      phone,
      dateOfBirth,
      gender,
      address,
      city,
      country,
      profileImagePath,
      isProfileComplete,
      lastUpdated,
    );
  }
}
