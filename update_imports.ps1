# PowerShell script to update all import statements for feature-based structure

# Define the mapping of old paths to new paths
$importMappings = @{
    # Core imports (no change needed)
    'package:kind_ali/core/' = 'package:kind_ali/core/'

    # Constants imports (commonly missing)
    'package:kind_ali/constants/app_colors.dart' = 'package:kind_ali/core/constants/app_colors.dart'
    'package:kind_ali/constants/app_dimensions.dart' = 'package:kind_ali/core/constants/app_dimensions.dart'
    'package:kind_ali/constants/app_text_styles.dart' = 'package:kind_ali/core/constants/app_text_styles.dart'
    'package:kind_ali/constants/app_images.dart' = 'package:kind_ali/core/constants/app_images.dart'
    'package:kind_ali/constants/app_constants.dart' = 'package:kind_ali/core/constants/app_constants.dart'

    # Helper functions
    'package:kind_ali/helper functions/string_extention_helper.dart' = 'package:kind_ali/core/extensions/string_extensions.dart'
    'package:kind_ali/helper%20functions/string_extention_helper.dart' = 'package:kind_ali/core/extensions/string_extensions.dart'

    # Routes
    'package:kind_ali/routes/app_routes.dart' = 'package:kind_ali/presentation/routes/app_routes.dart'
    
    # Data models moved to features
    'package:kind_ali/data/models/auth_model.dart' = 'package:kind_ali/features/authentication/data/models/auth_model.dart'
    'package:kind_ali/data/models/user_model.dart' = 'package:kind_ali/features/authentication/data/models/user_model.dart'
    'package:kind_ali/data/models/tourist_place_model.dart' = 'package:kind_ali/features/home/<USER>/models/tourist_place_model.dart'
    'package:kind_ali/data/models/hotel_details.dart' = 'package:kind_ali/features/hotel/data/models/hotel_details.dart'
    'package:kind_ali/data/models/hotel_offers.dart' = 'package:kind_ali/features/hotel/data/models/hotel_offers.dart'
    'package:kind_ali/data/models/hotel_reviews.dart' = 'package:kind_ali/features/hotel/data/models/hotel_reviews.dart'
    'package:kind_ali/data/models/hotel_rooms.dart' = 'package:kind_ali/features/hotel/data/models/hotel_rooms.dart'
    'package:kind_ali/data/models/booking_model.dart' = 'package:kind_ali/features/booking/data/models/booking_model.dart'
    'package:kind_ali/data/models/search_cities.dart' = 'package:kind_ali/features/search/data/models/search_cities.dart'
    'package:kind_ali/data/models/recent_search.dart' = 'package:kind_ali/features/search/data/models/recent_search.dart'

    # Model imports without package prefix (commonly used)
    'package:kind_ali/models/auth_model.dart' = 'package:kind_ali/features/authentication/data/models/auth_model.dart'
    'package:kind_ali/models/user_model.dart' = 'package:kind_ali/features/authentication/data/models/user_model.dart'
    'package:kind_ali/models/tourist_place_model.dart' = 'package:kind_ali/features/home/<USER>/models/tourist_place_model.dart'
    'package:kind_ali/models/hotel_details.dart' = 'package:kind_ali/features/hotel/data/models/hotel_details.dart'
    'package:kind_ali/models/hotel_offers.dart' = 'package:kind_ali/features/hotel/data/models/hotel_offers.dart'
    'package:kind_ali/models/hotel_reviews.dart' = 'package:kind_ali/features/hotel/data/models/hotel_reviews.dart'
    'package:kind_ali/models/hotel_rooms.dart' = 'package:kind_ali/features/hotel/data/models/hotel_rooms.dart'
    'package:kind_ali/models/booking_model.dart' = 'package:kind_ali/features/booking/data/models/booking_model.dart'
    'package:kind_ali/models/search_cities.dart' = 'package:kind_ali/features/search/data/models/search_cities.dart'
    'package:kind_ali/models/recent_search.dart' = 'package:kind_ali/features/search/data/models/recent_search.dart'
    
    # Domain entities moved to features
    'package:kind_ali/domain/entities/user_entity.dart' = 'package:kind_ali/features/authentication/domain/entities/user_entity.dart'
    'package:kind_ali/domain/entities/hotel_entity.dart' = 'package:kind_ali/features/hotel/domain/entities/hotel_entity.dart'
    'package:kind_ali/domain/entities/room_entity.dart' = 'package:kind_ali/features/hotel/domain/entities/room_entity.dart'
    'package:kind_ali/domain/entities/booking_entity.dart' = 'package:kind_ali/features/booking/domain/entities/booking_entity.dart'
    'package:kind_ali/domain/entities/search_entity.dart' = 'package:kind_ali/features/search/domain/entities/search_entity.dart'
    
    # Domain repositories moved to features
    'package:kind_ali/domain/repositories/auth_repository.dart' = 'package:kind_ali/features/authentication/domain/repositories/auth_repository.dart'
    'package:kind_ali/domain/repositories/user_repository.dart' = 'package:kind_ali/features/authentication/domain/repositories/user_repository.dart'
    'package:kind_ali/domain/repositories/hotel_repository.dart' = 'package:kind_ali/features/hotel/domain/repositories/hotel_repository.dart'
    'package:kind_ali/domain/repositories/booking_repository.dart' = 'package:kind_ali/features/booking/domain/repositories/booking_repository.dart'
    'package:kind_ali/domain/repositories/search_repository.dart' = 'package:kind_ali/features/search/domain/repositories/search_repository.dart'
    
    # Data repositories moved to features
    'package:kind_ali/data/repositories/auth_repository_impl.dart' = 'package:kind_ali/features/authentication/data/repositories/auth_repository_impl.dart'
    'package:kind_ali/data/repositories/user_repository_impl.dart' = 'package:kind_ali/features/authentication/data/repositories/user_repository_impl.dart'
    'package:kind_ali/data/repositories/hotel_repository_impl.dart' = 'package:kind_ali/features/hotel/data/repositories/hotel_repository_impl.dart'
    'package:kind_ali/data/repositories/booking_repository_impl.dart' = 'package:kind_ali/features/booking/data/repositories/booking_repository_impl.dart'
    'package:kind_ali/data/repositories/search_repository_impl.dart' = 'package:kind_ali/features/search/data/repositories/search_repository_impl.dart'
    
    # Data sources moved to features
    'package:kind_ali/data/datasources/local/auth_local_datasource.dart' = 'package:kind_ali/features/authentication/data/datasources/auth_local_datasource.dart'
    'package:kind_ali/data/datasources/remote/auth_remote_datasource.dart' = 'package:kind_ali/features/authentication/data/datasources/auth_remote_datasource.dart'
    'package:kind_ali/data/datasources/remote/auth_service.dart' = 'package:kind_ali/features/authentication/data/datasources/auth_service.dart'
    'package:kind_ali/data/datasources/local/hotel_local_datasource.dart' = 'package:kind_ali/features/hotel/data/datasources/hotel_local_datasource.dart'
    'package:kind_ali/data/datasources/remote/hotel_remote_datasource.dart' = 'package:kind_ali/features/hotel/data/datasources/hotel_remote_datasource.dart'
    'package:kind_ali/data/datasources/remote/hotel_service.dart' = 'package:kind_ali/features/hotel/data/datasources/hotel_service.dart'
    'package:kind_ali/data/datasources/local/booking_local_datasource.dart' = 'package:kind_ali/features/booking/data/datasources/booking_local_datasource.dart'
    'package:kind_ali/data/datasources/remote/booking_service.dart' = 'package:kind_ali/features/booking/data/datasources/booking_service.dart'
    'package:kind_ali/data/datasources/local/search_local_datasource.dart' = 'package:kind_ali/features/search/data/datasources/search_local_datasource.dart'
    'package:kind_ali/data/datasources/remote/api_service.dart' = 'package:kind_ali/shared/data/datasources/api_service.dart'
    
    # Presentation screens moved to features
    'package:kind_ali/presentation/screens/homescreen/' = 'package:kind_ali/features/home/<USER>/pages/home/'
    'package:kind_ali/presentation/screens/dashboard%20screen/' = 'package:kind_ali/features/home/<USER>/pages/dashboard/'
    'package:kind_ali/presentation/screens/hotel%20list%20screen/' = 'package:kind_ali/features/hotel/presentation/pages/hotel_list/'
    'package:kind_ali/presentation/screens/hotel%20detail%20screen/' = 'package:kind_ali/features/hotel/presentation/pages/hotel_detail/'
    'package:kind_ali/presentation/screens/booking%20screen/' = 'package:kind_ali/features/booking/presentation/pages/booking/'
    'package:kind_ali/presentation/screens/room%20selection%20screen/' = 'package:kind_ali/features/booking/presentation/pages/room_selection/'
    'package:kind_ali/presentation/screens/payment%20interface/' = 'package:kind_ali/features/booking/presentation/pages/payment/'
    'package:kind_ali/presentation/screens/profile%20screen/' = 'package:kind_ali/features/profile/presentation/pages/profile/'
    'package:kind_ali/presentation/screens/language%20screen/' = 'package:kind_ali/features/profile/presentation/pages/language/'
    'package:kind_ali/presentation/screens/wishlist%20screen/' = 'package:kind_ali/features/wishlist/presentation/pages/wishlist/'
    'package:kind_ali/presentation/screens/itinerary%20screen/' = 'package:kind_ali/features/itinerary/presentation/pages/itinerary/'
    'package:kind_ali/presentation/screens/mapview%20screen/' = 'package:kind_ali/features/search/presentation/pages/mapview/'
    'package:kind_ali/presentation/screens/splash%20screen/' = 'package:kind_ali/shared/presentation/pages/splash/'

    # Screen imports without package prefix (commonly used)
    'package:kind_ali/screens/homescreen/' = 'package:kind_ali/features/home/<USER>/pages/home/'
    'package:kind_ali/screens/dashboard%20screen/' = 'package:kind_ali/features/home/<USER>/pages/dashboard/'
    'package:kind_ali/screens/hotel%20list%20screen/' = 'package:kind_ali/features/hotel/presentation/pages/hotel_list/'
    'package:kind_ali/screens/hotel%20detail%20screen/' = 'package:kind_ali/features/hotel/presentation/pages/hotel_detail/'
    'package:kind_ali/screens/booking%20screen/' = 'package:kind_ali/features/booking/presentation/pages/booking/'
    'package:kind_ali/screens/room%20selection%20screen/' = 'package:kind_ali/features/booking/presentation/pages/room_selection/'
    'package:kind_ali/screens/payment%20interface/' = 'package:kind_ali/features/booking/presentation/pages/payment/'
    'package:kind_ali/screens/profile%20screen/' = 'package:kind_ali/features/profile/presentation/pages/profile/'
    'package:kind_ali/screens/language%20screen/' = 'package:kind_ali/features/profile/presentation/pages/language/'
    'package:kind_ali/screens/wishlist%20screen/' = 'package:kind_ali/features/wishlist/presentation/pages/wishlist/'
    'package:kind_ali/screens/itinerary%20screen/' = 'package:kind_ali/features/itinerary/presentation/pages/itinerary/'
    'package:kind_ali/screens/mapview%20screen/' = 'package:kind_ali/features/search/presentation/pages/mapview/'
    'package:kind_ali/screens/splash%20screen/' = 'package:kind_ali/shared/presentation/pages/splash/'
    
    # Presentation providers moved to features
    'package:kind_ali/presentation/providers/auth_notifier.dart' = 'package:kind_ali/features/authentication/presentation/providers/auth_notifier.dart'
    'package:kind_ali/presentation/providers/home_notifier.dart' = 'package:kind_ali/features/home/<USER>/providers/home_notifier.dart'
    'package:kind_ali/presentation/providers/hotel_details_notifier.dart' = 'package:kind_ali/features/hotel/presentation/providers/hotel_details_notifier.dart'
    'package:kind_ali/presentation/providers/hotel_list_notifier.dart' = 'package:kind_ali/features/hotel/presentation/providers/hotel_list_notifier.dart'
    'package:kind_ali/presentation/providers/booking_notifier.dart' = 'package:kind_ali/features/booking/presentation/providers/booking_notifier.dart'
    'package:kind_ali/presentation/providers/room_selection_notifier.dart' = 'package:kind_ali/features/booking/presentation/providers/room_selection_notifier.dart'
    'package:kind_ali/presentation/providers/profile_notifier.dart' = 'package:kind_ali/features/profile/presentation/providers/profile_notifier.dart'
    'package:kind_ali/presentation/providers/search_cities_notifier.dart' = 'package:kind_ali/features/search/presentation/providers/search_cities_notifier.dart'
    'package:kind_ali/presentation/providers/wishlist_notifier.dart' = 'package:kind_ali/features/wishlist/presentation/providers/wishlist_notifier.dart'
    'package:kind_ali/presentation/providers/currency_notifier.dart' = 'package:kind_ali/shared/presentation/providers/currency_notifier.dart'
    'package:kind_ali/presentation/providers/localization_notifier.dart' = 'package:kind_ali/shared/presentation/providers/localization_notifier.dart'
    
    # Presentation widgets moved to shared
    'package:kind_ali/presentation/widgets/' = 'package:kind_ali/shared/presentation/widgets/'
    
    # Use cases moved to features
    'package:kind_ali/domain/usecases/base/' = 'package:kind_ali/shared/domain/usecases/base/'
    'package:kind_ali/domain/usecases/auth/' = 'package:kind_ali/features/authentication/domain/usecases/auth/'
    'package:kind_ali/domain/usecases/user/' = 'package:kind_ali/features/authentication/domain/usecases/user/'
    'package:kind_ali/domain/usecases/hotel/' = 'package:kind_ali/features/hotel/domain/usecases/hotel/'
    'package:kind_ali/domain/usecases/booking/' = 'package:kind_ali/features/booking/domain/usecases/booking/'
    'package:kind_ali/domain/usecases/search/' = 'package:kind_ali/features/search/domain/usecases/search/'

    # Specific file mappings that are causing issues
    '../screens/homescreen/widgets/guest_selection_widget.dart' = '../../../features/home/<USER>/pages/home/<USER>/guest_selection_widget.dart'
    '../../../core/constants/app_constants.dart' = '../../../../core/constants/app_constants.dart'
    '../../../core/error/exceptions.dart' = '../../../../core/error/exceptions.dart'
    '../../models/search_cities.dart' = '../models/search_cities.dart'
    '../../core/error/exceptions.dart' = '../../../core/error/exceptions.dart'
    '../../core/error/failures.dart' = '../../../core/error/failures.dart'
    '../../core/network/network_info.dart' = '../../../core/network/network_info.dart'
    '../datasources/local/search_local_datasource.dart' = '../datasources/search_local_datasource.dart'
    '../../models/hotel_details.dart' = '../../../hotel/data/models/hotel_details.dart'
}

# Function to update imports in a file
function Update-ImportsInFile {
    param(
        [string]$FilePath
    )
    
    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath -Raw
        $originalContent = $content
        
        foreach ($oldPath in $importMappings.Keys) {
            $newPath = $importMappings[$oldPath]
            $content = $content -replace [regex]::Escape($oldPath), $newPath
        }
        
        if ($content -ne $originalContent) {
            Set-Content -Path $FilePath -Value $content -NoNewline
            Write-Host "Updated imports in: $FilePath"
        }
    }
}

# Get all Dart files recursively
$dartFiles = Get-ChildItem -Path "lib" -Filter "*.dart" -Recurse

Write-Host "Updating imports in $($dartFiles.Count) Dart files..."

foreach ($file in $dartFiles) {
    Update-ImportsInFile -FilePath $file.FullName
}

Write-Host "Import update completed!"
