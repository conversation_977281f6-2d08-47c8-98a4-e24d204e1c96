import '../../domain/entities/user_entity.dart';

/// User model for data layer
class UserModel extends UserEntity {
  const UserModel({
    super.userId,
    super.firstName,
    super.lastName,
    super.email,
    super.phone,
    super.profileImageUrl,
    super.dateOfBirth,
    super.nationality,
    super.passportNumber,
    super.preferredLanguage,
    super.preferredCurrency,
    super.isEmailVerified,
    super.isPhoneVerified,
    super.createdAt,
    super.updatedAt,
    super.wishlistHotelIds,
  });

  /// Create UserModel from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      userId: json['user_id'] as String?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      profileImageUrl: json['profile_image_url'] as String?,
      dateOfBirth: json['date_of_birth'] != null
          ? DateTime.parse(json['date_of_birth'] as String)
          : null,
      nationality: json['nationality'] as String?,
      passportNumber: json['passport_number'] as String?,
      preferredLanguage: json['preferred_language'] as String?,
      preferredCurrency: json['preferred_currency'] as String?,
      isEmailVerified: json['is_email_verified'] as bool?,
      isPhoneVerified: json['is_phone_verified'] as bool?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      wishlistHotelIds: json['wishlist_hotel_ids'] != null
          ? List<String>.from(json['wishlist_hotel_ids'] as List)
          : null,
    );
  }

  /// Convert UserModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'phone': phone,
      'profile_image_url': profileImageUrl,
      'date_of_birth': dateOfBirth?.toIso8601String(),
      'nationality': nationality,
      'passport_number': passportNumber,
      'preferred_language': preferredLanguage,
      'preferred_currency': preferredCurrency,
      'is_email_verified': isEmailVerified,
      'is_phone_verified': isPhoneVerified,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'wishlist_hotel_ids': wishlistHotelIds,
    };
  }

  /// Create UserModel from UserEntity
  factory UserModel.fromEntity(UserEntity entity) {
    return UserModel(
      userId: entity.userId,
      firstName: entity.firstName,
      lastName: entity.lastName,
      email: entity.email,
      phone: entity.phone,
      profileImageUrl: entity.profileImageUrl,
      dateOfBirth: entity.dateOfBirth,
      nationality: entity.nationality,
      passportNumber: entity.passportNumber,
      preferredLanguage: entity.preferredLanguage,
      preferredCurrency: entity.preferredCurrency,
      isEmailVerified: entity.isEmailVerified,
      isPhoneVerified: entity.isPhoneVerified,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      wishlistHotelIds: entity.wishlistHotelIds,
    );
  }

  /// Convert to UserEntity
  UserEntity toEntity() {
    return UserEntity(
      userId: userId,
      firstName: firstName,
      lastName: lastName,
      email: email,
      phone: phone,
      profileImageUrl: profileImageUrl,
      dateOfBirth: dateOfBirth,
      nationality: nationality,
      passportNumber: passportNumber,
      preferredLanguage: preferredLanguage,
      preferredCurrency: preferredCurrency,
      isEmailVerified: isEmailVerified,
      isPhoneVerified: isPhoneVerified,
      createdAt: createdAt,
      updatedAt: updatedAt,
      wishlistHotelIds: wishlistHotelIds,
    );
  }

  /// Copy with method
  UserModel copyWith({
    String? userId,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? profileImageUrl,
    DateTime? dateOfBirth,
    String? nationality,
    String? passportNumber,
    String? preferredLanguage,
    String? preferredCurrency,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? wishlistHotelIds,
  }) {
    return UserModel(
      userId: userId ?? this.userId,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      nationality: nationality ?? this.nationality,
      passportNumber: passportNumber ?? this.passportNumber,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      preferredCurrency: preferredCurrency ?? this.preferredCurrency,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      wishlistHotelIds: wishlistHotelIds ?? this.wishlistHotelIds,
    );
  }
}
