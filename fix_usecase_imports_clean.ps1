# Clean up UseCase files and add proper imports
Write-Host "Cleaning up UseCase files and adding proper imports..." -ForegroundColor Cyan

$usecaseFiles = Get-ChildItem -Path "lib" -Recurse -Filter "*.dart" | Where-Object {
    $_.FullName -match "usecases" -and $_.FullName -notmatch "base"
}

$updatedFiles = 0

foreach ($file in $usecaseFiles) {
    $content = Get-Content $file.FullName -Raw
    
    # Check if file has UseCase implementation but missing import
    if ($content -match "implements\s+(UseCase|UseCaseNoParams)" -and $content -notmatch "shared/domain/usecases/base/usecase.dart") {
        Write-Host "Cleaning and fixing: $($file.Name)" -ForegroundColor Yellow
        
        # Split content into lines
        $lines = Get-Content $file.FullName
        
        # Find the first import line and the first class declaration
        $firstImportIndex = -1
        $firstClassIndex = -1
        
        for ($i = 0; $i -lt $lines.Length; $i++) {
            if ($lines[$i] -match "^import\s+" -and $firstImportIndex -eq -1) {
                $firstImportIndex = $i
            }
            if ($lines[$i] -match "^(class|abstract class)" -and $firstClassIndex -eq -1) {
                $firstClassIndex = $i
                break
            }
        }
        
        # Remove duplicate content (everything after first class declaration that looks like imports/classes)
        $cleanLines = @()
        $inDuplicateSection = $false
        
        for ($i = 0; $i -lt $lines.Length; $i++) {
            $line = $lines[$i]
            
            # If we hit a duplicate import after class declaration, start removing
            if ($i -gt $firstClassIndex -and $line -match "^import\s+") {
                $inDuplicateSection = $true
                continue
            }
            
            # If we're in duplicate section and hit another class, skip it
            if ($inDuplicateSection -and $line -match "^(class|abstract class)") {
                continue
            }
            
            # If we're in duplicate section and hit end of file or empty lines, we might be done
            if ($inDuplicateSection -and ($line.Trim() -eq "" -or $i -eq $lines.Length - 1)) {
                # Check if this is truly the end
                $remainingLines = $lines[($i+1)..($lines.Length-1)]
                $hasMoreContent = $false
                foreach ($remainingLine in $remainingLines) {
                    if ($remainingLine.Trim() -ne "" -and $remainingLine -notmatch "^(import|class|abstract class)") {
                        $hasMoreContent = $true
                        break
                    }
                }
                if (-not $hasMoreContent) {
                    break
                }
            }
            
            # If not in duplicate section, keep the line
            if (-not $inDuplicateSection) {
                $cleanLines += $line
            }
        }
        
        # Add the UseCase import after other imports
        $finalLines = @()
        $importAdded = $false
        
        for ($i = 0; $i -lt $cleanLines.Length; $i++) {
            $line = $cleanLines[$i]
            $finalLines += $line
            
            # If this is an import line and the next line is not an import, add our import
            if ($line -match "^import\s+" -and -not $importAdded) {
                $nextLineIsImport = $false
                if ($i + 1 -lt $cleanLines.Length) {
                    $nextLineIsImport = $cleanLines[$i + 1] -match "^import\s+"
                }
                
                if (-not $nextLineIsImport) {
                    $finalLines += "import 'package:kind_ali/shared/domain/usecases/base/usecase.dart';"
                    $importAdded = $true
                }
            }
        }
        
        # Write the cleaned content back to file
        $finalLines | Set-Content $file.FullName -Encoding UTF8
        $updatedFiles++
        Write-Host "Cleaned and fixed: $($file.Name)" -ForegroundColor Green
    }
}

Write-Host "Cleaned and updated $updatedFiles UseCase files" -ForegroundColor Yellow
