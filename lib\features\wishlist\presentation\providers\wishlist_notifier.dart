/// Wishlist state management using Riverpod StateNotifier
library;

import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../hotel/data/models/hotel_details.dart';

/// Wishlist state class
class WishlistState {
  final List<InventoryInfoList> wishlistItems;
  final bool isLoading;

  const WishlistState({
    this.wishlistItems = const [],
    this.isLoading = false,
  });

  /// Check if wishlist is empty
  bool get isEmpty => wishlistItems.isEmpty;

  /// Get item count
  int get itemCount => wishlistItems.length;

  WishlistState copyWith({
    List<InventoryInfoList>? wishlistItems,
    bool? isLoading,
  }) {
    return WishlistState(
      wishlistItems: wishlistItems ?? this.wishlistItems,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WishlistState &&
        other.wishlistItems == wishlistItems &&
        other.isLoading == isLoading;
  }

  @override
  int get hashCode => Object.hash(wishlistItems, isLoading);
}

/// Wishlist StateNotifier
class WishlistNotifier extends StateNotifier<WishlistState> {
  static const String _wishlistKey = 'wishlist_items';

  WishlistNotifier() : super(const WishlistState()) {
    _loadWishlistFromStorage();
  }

  /// Check if a hotel is in the wishlist
  bool isInWishlist(String hotelId) {
    return state.wishlistItems.any((hotel) => hotel.hotelId?.toString() == hotelId);
  }

  /// Add hotel to wishlist
  Future<void> addToWishlist(InventoryInfoList hotel) async {
    if (!isInWishlist(hotel.hotelId?.toString() ?? '')) {
      final updatedItems = [...state.wishlistItems, hotel];
      state = state.copyWith(wishlistItems: updatedItems);
      await _saveWishlistToStorage();
    }
  }

  /// Remove hotel from wishlist
  Future<void> removeFromWishlist(String hotelId) async {
    final updatedItems = state.wishlistItems
        .where((hotel) => hotel.hotelId?.toString() != hotelId)
        .toList();
    state = state.copyWith(wishlistItems: updatedItems);
    await _saveWishlistToStorage();
  }

  /// Toggle hotel in wishlist
  Future<void> toggleWishlist(InventoryInfoList hotel) async {
    final hotelId = hotel.hotelId?.toString() ?? '';
    if (isInWishlist(hotelId)) {
      await removeFromWishlist(hotelId);
    } else {
      await addToWishlist(hotel);
    }
  }

  /// Clear all wishlist items
  Future<void> clearWishlist() async {
    state = state.copyWith(wishlistItems: []);
    await _saveWishlistToStorage();
  }

  /// Load wishlist from SharedPreferences
  Future<void> _loadWishlistFromStorage() async {
    try {
      state = state.copyWith(isLoading: true);
      final prefs = await SharedPreferences.getInstance();
      final wishlistJson = prefs.getString(_wishlistKey);
      
      if (wishlistJson != null) {
        final List<dynamic> jsonList = json.decode(wishlistJson);
        final List<InventoryInfoList> items = jsonList
            .map((json) => InventoryInfoList.fromJson(json))
            .toList();
        state = state.copyWith(
          wishlistItems: items,
          isLoading: false,
        );
      } else {
        state = state.copyWith(isLoading: false);
      }
    } catch (e) {
      // Handle error silently
      state = state.copyWith(isLoading: false);
    }
  }

  /// Save wishlist to SharedPreferences
  Future<void> _saveWishlistToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = state.wishlistItems.map((hotel) => hotel.toJson()).toList();
      final wishlistJson = json.encode(jsonList);
      await prefs.setString(_wishlistKey, wishlistJson);
    } catch (e) {
      // Handle error silently
    }
  }
}

/// Wishlist provider
final wishlistProvider = StateNotifierProvider<WishlistNotifier, WishlistState>(
  (ref) => WishlistNotifier(),
);
