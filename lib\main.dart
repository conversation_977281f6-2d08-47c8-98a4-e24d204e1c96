import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/core/constants/app_localizations.dart';
import 'package:kind_ali/core/themes/themes.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';
import 'package:kind_ali/presentation/routes/app_routes.dart';
import 'package:kind_ali/shared/presentation/providers/localization_notifier.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(
    ProviderScope(
      child: MyApp(),
    ),
  );
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localizationState = ref.watch(localizationProvider);

    return MaterialApp(
      title: 'kind_ali',
      debugShowCheckedModeBanner: false,

      // Localization setup - Using Riverpod provider
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      locale: localizationState.locale,
      supportedLocales: LocalizationNotifier.supportedLocales,

      builder: (context, child) {
        setGlobalContext(context);
        return child!;
      },

      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeConfig.themeMode,

      initialRoute: AppRoutes.splash,
      routes: AppRoutes.getRoutes(),
      onGenerateRoute: AppRoutes.generateRoute,
    );
  }
}
