import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/core/constants/app_localizations.dart';
import 'package:kind_ali/core/themes/themes.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';
import 'package:kind_ali/presentation/routes/app_routes.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(
    ProviderScope(
      child: MyApp(),
    ),
  );
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // For now, we'll use default locale settings since localization providers need to be migrated
    // TODO: Migrate localization to Riverpod in next phase
    return MaterialApp(
      title: 'kind_ali',
      debugShowCheckedModeBanner: false,

      // Localization setup - Using default for now
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en', 'US'),
        Locale('ar', 'SA'),
      ],

      builder: (context, child) {
        setGlobalContext(context);
        return child!;
      },

      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeConfig.themeMode,

      initialRoute: AppRoutes.splash,
      routes: AppRoutes.getRoutes(),
      onGenerateRoute: AppRoutes.generateRoute,
    );
  }
}
