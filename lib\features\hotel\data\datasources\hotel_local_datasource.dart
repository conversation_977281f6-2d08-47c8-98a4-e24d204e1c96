import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:kind_ali/core/constants/app_constants.dart';
import 'package:kind_ali/core/error/exceptions.dart';
import '../models/hotel_details.dart';

/// Abstract class for hotel local data source
abstract class HotelLocalDataSource {
  /// Get hotels from cache
  Future<List<InventoryInfoList>> getHotelsFromCache();
  
  /// Cache hotels data
  Future<void> cacheHotels(List<InventoryInfoList> hotels);
  
  /// Get hotels from local JSON assets
  Future<List<InventoryInfoList>> getHotelsFromAssets();
  
  /// Get hotel details from cache
  Future<InventoryInfoList?> getHotelDetailsFromCache(int hotelId);
  
  /// Cache hotel details
  Future<void> cacheHotelDetails(InventoryInfoList hotel);
  
  /// Clear hotels cache
  Future<void> clearHotelsCache();
  
  /// Check if hotels cache is valid
  Future<bool> isHotelsCacheValid();
}

/// Implementation of hotel local data source
class HotelLocalDataSourceImpl implements HotelLocalDataSource {
  final SharedPreferences sharedPreferences;
  
  HotelLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<List<InventoryInfoList>> getHotelsFromAssets() async {
    try {
      final String response = await rootBundle.loadString(
        '${AppConstants.jsonPath}${AppConstants.hotelsJsonFile}',
      );
      final Map<String, dynamic> jsonData = json.decode(response);
      final Hotel hotel = Hotel.fromJson(jsonData);
      return hotel.data?.result?.inventoryInfoList ?? [];
    } catch (e) {
      throw FileSystemException('Failed to load hotels from assets: $e');
    }
  }

  @override
  Future<List<InventoryInfoList>> getHotelsFromCache() async {
    try {
      final jsonString = sharedPreferences.getString(AppConstants.keyCachedHotels);
      if (jsonString != null) {
        final List<dynamic> jsonData = json.decode(jsonString);
        return jsonData.map((e) => InventoryInfoList.fromJson(e)).toList();
      }
      throw CacheException('No hotels found in cache');
    } catch (e) {
      throw CacheException('Failed to get hotels from cache: $e');
    }
  }

  @override
  Future<void> cacheHotels(List<InventoryInfoList> hotels) async {
    try {
      final jsonString = json.encode(hotels.map((e) => e.toJson()).toList());
      await sharedPreferences.setString(AppConstants.keyCachedHotels, jsonString);
      
      // Store cache timestamp
      await sharedPreferences.setInt(
        '${AppConstants.keyCachedHotels}_timestamp',
        DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      throw CacheException('Failed to cache hotels: $e');
    }
  }

  @override
  Future<InventoryInfoList?> getHotelDetailsFromCache(int hotelId) async {
    try {
      final hotels = await getHotelsFromCache();
      return hotels.firstWhere(
        (hotel) => hotel.hotelId == hotelId,
        orElse: () => throw CacheException('Hotel not found in cache'),
      );
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> cacheHotelDetails(InventoryInfoList hotel) async {
    try {
      final hotels = await getHotelsFromCache();
      final index = hotels.indexWhere((h) => h.hotelId == hotel.hotelId);
      
      if (index != -1) {
        hotels[index] = hotel;
      } else {
        hotels.add(hotel);
      }
      
      await cacheHotels(hotels);
    } catch (e) {
      // If cache doesn't exist, create new cache with this hotel
      await cacheHotels([hotel]);
    }
  }

  @override
  Future<void> clearHotelsCache() async {
    try {
      await sharedPreferences.remove(AppConstants.keyCachedHotels);
      await sharedPreferences.remove('${AppConstants.keyCachedHotels}_timestamp');
    } catch (e) {
      throw CacheException('Failed to clear hotels cache: $e');
    }
  }

  @override
  Future<bool> isHotelsCacheValid() async {
    try {
      final timestamp = sharedPreferences.getInt('${AppConstants.keyCachedHotels}_timestamp');
      if (timestamp == null) return false;
      
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final now = DateTime.now();
      final difference = now.difference(cacheTime).inHours;
      
      return difference < AppConstants.cacheValidityDuration;
    } catch (e) {
      return false;
    }
  }
}
