import 'package:kind_ali/core/utils/enums.dart';
import '../../domain/entities/booking_entity.dart';

/// Booking model for data layer
class BookingModel extends BookingEntity {
  const BookingModel({
    super.bookingId,
    super.hotelId,
    super.hotelName,
    super.roomType,
    super.checkInDate,
    super.checkOutDate,
    super.numberOfGuests,
    super.numberOfRooms,
    super.totalAmount,
    super.currency,
    super.status,
    super.paymentStatus,
    super.bookingDate,
    super.cancellationDate,
    super.guestDetails,
    super.paymentDetails,
    super.specialRequests,
    super.confirmationNumber,
  });

  /// Create BookingModel from JSON
  factory BookingModel.fromJson(Map<String, dynamic> json) {
    return BookingModel(
      bookingId: json['bookingId'] as String?,
      hotelId: json['hotelId'] as String?,
      hotelName: json['hotelName'] as String?,
      roomType: json['roomType'] as String?,
      checkInDate: json['checkInDate'] != null 
          ? DateTime.parse(json['checkInDate'] as String)
          : null,
      checkOutDate: json['checkOutDate'] != null 
          ? DateTime.parse(json['checkOutDate'] as String)
          : null,
      numberOfGuests: json['numberOfGuests'] as int?,
      numberOfRooms: json['numberOfRooms'] as int?,
      totalAmount: (json['totalAmount'] as num?)?.toDouble(),
      currency: json['currency'] as String?,
      status: json['status'] != null 
          ? BookingStatus.values.firstWhere(
              (e) => e.toString().split('.').last == json['status'],
              orElse: () => BookingStatus.pending,
            )
          : null,
      paymentStatus: json['paymentStatus'] != null 
          ? PaymentStatus.values.firstWhere(
              (e) => e.toString().split('.').last == json['paymentStatus'],
              orElse: () => PaymentStatus.pending,
            )
          : null,
      bookingDate: json['bookingDate'] != null 
          ? DateTime.parse(json['bookingDate'] as String)
          : null,
      cancellationDate: json['cancellationDate'] != null 
          ? DateTime.parse(json['cancellationDate'] as String)
          : null,
      guestDetails: json['guestDetails'] != null 
          ? GuestDetailsModel.fromJson(json['guestDetails'] as Map<String, dynamic>)
          : null,
      paymentDetails: json['paymentDetails'] != null 
          ? PaymentDetailsModel.fromJson(json['paymentDetails'] as Map<String, dynamic>)
          : null,
      specialRequests: json['specialRequests'] as String?,
      confirmationNumber: json['confirmationNumber'] as String?,
    );
  }

  /// Convert BookingModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'bookingId': bookingId,
      'hotelId': hotelId,
      'hotelName': hotelName,
      'roomType': roomType,
      'checkInDate': checkInDate?.toIso8601String(),
      'checkOutDate': checkOutDate?.toIso8601String(),
      'numberOfGuests': numberOfGuests,
      'numberOfRooms': numberOfRooms,
      'totalAmount': totalAmount,
      'currency': currency,
      'status': status?.toString().split('.').last,
      'paymentStatus': paymentStatus?.toString().split('.').last,
      'bookingDate': bookingDate?.toIso8601String(),
      'cancellationDate': cancellationDate?.toIso8601String(),
      'guestDetails': (guestDetails as GuestDetailsModel?)?.toJson(),
      'paymentDetails': (paymentDetails as PaymentDetailsModel?)?.toJson(),
      'specialRequests': specialRequests,
      'confirmationNumber': confirmationNumber,
    };
  }

  /// Create BookingModel from entity
  factory BookingModel.fromEntity(BookingEntity entity) {
    return BookingModel(
      bookingId: entity.bookingId,
      hotelId: entity.hotelId,
      hotelName: entity.hotelName,
      roomType: entity.roomType,
      checkInDate: entity.checkInDate,
      checkOutDate: entity.checkOutDate,
      numberOfGuests: entity.numberOfGuests,
      numberOfRooms: entity.numberOfRooms,
      totalAmount: entity.totalAmount,
      currency: entity.currency,
      status: entity.status,
      paymentStatus: entity.paymentStatus,
      bookingDate: entity.bookingDate,
      cancellationDate: entity.cancellationDate,
      guestDetails: entity.guestDetails != null 
          ? GuestDetailsModel.fromEntity(entity.guestDetails!)
          : null,
      paymentDetails: entity.paymentDetails != null 
          ? PaymentDetailsModel.fromEntity(entity.paymentDetails!)
          : null,
      specialRequests: entity.specialRequests,
      confirmationNumber: entity.confirmationNumber,
    );
  }

  /// Convert to entity
  BookingEntity toEntity() {
    return BookingEntity(
      bookingId: bookingId,
      hotelId: hotelId,
      hotelName: hotelName,
      roomType: roomType,
      checkInDate: checkInDate,
      checkOutDate: checkOutDate,
      numberOfGuests: numberOfGuests,
      numberOfRooms: numberOfRooms,
      totalAmount: totalAmount,
      currency: currency,
      status: status,
      paymentStatus: paymentStatus,
      bookingDate: bookingDate,
      cancellationDate: cancellationDate,
      guestDetails: guestDetails,
      paymentDetails: paymentDetails,
      specialRequests: specialRequests,
      confirmationNumber: confirmationNumber,
    );
  }

  /// Create a copy with updated values
  @override
  BookingModel copyWith({
    String? bookingId,
    String? hotelId,
    String? hotelName,
    String? roomId,
    String? roomType,
    DateTime? checkInDate,
    DateTime? checkOutDate,
    int? numberOfGuests,
    int? numberOfRooms,
    int? numberOfNights,
    double? totalAmount,
    String? currency,
    BookingStatus? status,
    PaymentStatus? paymentStatus,
    DateTime? bookingDate,
    DateTime? cancellationDate,
    GuestDetailsEntity? guestDetails,
    PaymentDetailsEntity? paymentDetails,
    String? specialRequests,
    String? confirmationNumber,
    bool? isBreakfastIncluded,
    String? cancellationPolicy,
  }) {
    return BookingModel(
      bookingId: bookingId ?? this.bookingId,
      hotelId: hotelId ?? this.hotelId,
      hotelName: hotelName ?? this.hotelName,
      roomType: roomType ?? this.roomType,
      checkInDate: checkInDate ?? this.checkInDate,
      checkOutDate: checkOutDate ?? this.checkOutDate,
      numberOfGuests: numberOfGuests ?? this.numberOfGuests,
      numberOfRooms: numberOfRooms ?? this.numberOfRooms,
      totalAmount: totalAmount ?? this.totalAmount,
      currency: currency ?? this.currency,
      status: status ?? this.status,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      bookingDate: bookingDate ?? this.bookingDate,
      cancellationDate: cancellationDate ?? this.cancellationDate,
      guestDetails: guestDetails ?? this.guestDetails,
      paymentDetails: paymentDetails ?? this.paymentDetails,
      specialRequests: specialRequests ?? this.specialRequests,
      confirmationNumber: confirmationNumber ?? this.confirmationNumber,
    );
  }
}

/// Guest details model
class GuestDetailsModel extends GuestDetailsEntity {
  const GuestDetailsModel({
    super.firstName,
    super.lastName,
    super.email,
    super.phone,
    super.nationality,
    super.passportNumber,
    super.dateOfBirth,
    super.specialRequests,
  });

  factory GuestDetailsModel.fromJson(Map<String, dynamic> json) {
    return GuestDetailsModel(
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      nationality: json['nationality'] as String?,
      passportNumber: json['passportNumber'] as String?,
      dateOfBirth: json['dateOfBirth'] != null 
          ? DateTime.parse(json['dateOfBirth'] as String)
          : null,
      specialRequests: json['specialRequests'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phone': phone,
      'nationality': nationality,
      'passportNumber': passportNumber,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'specialRequests': specialRequests,
    };
  }

  factory GuestDetailsModel.fromEntity(GuestDetailsEntity entity) {
    return GuestDetailsModel(
      firstName: entity.firstName,
      lastName: entity.lastName,
      email: entity.email,
      phone: entity.phone,
      nationality: entity.nationality,
      passportNumber: entity.passportNumber,
      dateOfBirth: entity.dateOfBirth,
      specialRequests: entity.specialRequests,
    );
  }
}

/// Payment details model
class PaymentDetailsModel extends PaymentDetailsEntity {
  const PaymentDetailsModel({
    super.paymentMethod,
    super.transactionId,
    super.paymentDate,
    super.amount,
    super.currency,
    super.cardLastFourDigits,
    super.paymentGateway,
  });

  factory PaymentDetailsModel.fromJson(Map<String, dynamic> json) {
    return PaymentDetailsModel(
      paymentMethod: json['paymentMethod'] as String?,
      transactionId: json['transactionId'] as String?,
      paymentDate: json['paymentDate'] != null 
          ? DateTime.parse(json['paymentDate'] as String)
          : null,
      amount: (json['amount'] as num?)?.toDouble(),
      currency: json['currency'] as String?,
      cardLastFourDigits: json['cardLastFourDigits'] as String?,
      paymentGateway: json['paymentGateway'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'paymentMethod': paymentMethod,
      'transactionId': transactionId,
      'paymentDate': paymentDate?.toIso8601String(),
      'amount': amount,
      'currency': currency,
      'cardLastFourDigits': cardLastFourDigits,
      'paymentGateway': paymentGateway,
    };
  }

  factory PaymentDetailsModel.fromEntity(PaymentDetailsEntity entity) {
    return PaymentDetailsModel(
      paymentMethod: entity.paymentMethod,
      transactionId: entity.transactionId,
      paymentDate: entity.paymentDate,
      amount: entity.amount,
      currency: entity.currency,
      cardLastFourDigits: entity.cardLastFourDigits,
      paymentGateway: entity.paymentGateway,
    );
  }
}
