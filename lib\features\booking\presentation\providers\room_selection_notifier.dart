/// Room selection state management using Riverpod StateNotifier
library;

import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/features/hotel/data/models/hotel_rooms.dart';

/// Selected room option class
class SelectedRoomOption {
  final String roomId;
  final String roomType;
  final double price;
  final int quantity;
  final Room room;
  final RoomOption roomOption;

  const SelectedRoomOption({
    required this.roomId,
    required this.roomType,
    required this.price,
    required this.room,
    required this.roomOption,
    this.quantity = 1,
  });

  SelectedRoomOption copyWith({
    String? roomId,
    String? roomType,
    double? price,
    int? quantity,
    Room? room,
    RoomOption? roomOption,
  }) {
    return SelectedRoomOption(
      roomId: roomId ?? this.roomId,
      roomType: roomType ?? this.roomType,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      room: room ?? this.room,
      roomOption: roomOption ?? this.roomOption,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SelectedRoomOption &&
        other.roomId == roomId &&
        other.roomType == roomType &&
        other.price == price &&
        other.quantity == quantity;
  }

  @override
  int get hashCode => Object.hash(roomId, roomType, price, quantity);
}

/// Room selection state class
class RoomSelectionState {
  final Hotelrooms? hotelrooms;
  final List<Room> rooms;
  final String selectedSortOption;
  final List<SelectedRoomOption> selectedRoomOptions;
  final int maxRoomCount;
  final int totalGuests;
  final bool isLoading;
  final String? error;

  const RoomSelectionState({
    this.hotelrooms,
    this.rooms = const [],
    this.selectedSortOption = 'all',
    this.selectedRoomOptions = const [],
    this.maxRoomCount = 1,
    this.totalGuests = 2,
    this.isLoading = false,
    this.error,
  });

  RoomSelectionState copyWith({
    Hotelrooms? hotelrooms,
    List<Room>? rooms,
    String? selectedSortOption,
    List<SelectedRoomOption>? selectedRoomOptions,
    int? maxRoomCount,
    int? totalGuests,
    bool? isLoading,
    String? error,
  }) {
    return RoomSelectionState(
      hotelrooms: hotelrooms ?? this.hotelrooms,
      rooms: rooms ?? this.rooms,
      selectedSortOption: selectedSortOption ?? this.selectedSortOption,
      selectedRoomOptions: selectedRoomOptions ?? this.selectedRoomOptions,
      maxRoomCount: maxRoomCount ?? this.maxRoomCount,
      totalGuests: totalGuests ?? this.totalGuests,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RoomSelectionState &&
        other.hotelrooms == hotelrooms &&
        other.rooms == rooms &&
        other.selectedSortOption == selectedSortOption &&
        other.selectedRoomOptions == selectedRoomOptions &&
        other.maxRoomCount == maxRoomCount &&
        other.totalGuests == totalGuests &&
        other.isLoading == isLoading &&
        other.error == error;
  }

  @override
  int get hashCode => Object.hash(
        hotelrooms,
        rooms,
        selectedSortOption,
        selectedRoomOptions,
        maxRoomCount,
        totalGuests,
        isLoading,
        error,
      );
}

/// Room selection StateNotifier
class RoomSelectionNotifier extends StateNotifier<RoomSelectionState> {
  RoomSelectionNotifier() : super(const RoomSelectionState()) {
    loadHotelRoomsFromJson();
  }

  /// Load hotel rooms from JSON
  Future<void> loadHotelRoomsFromJson() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final String jsonString = await rootBundle.loadString('assets/json/hotelleveldetails.json');
      final hotelrooms = hotelroomsFromJson(jsonString);
      final rooms = hotelrooms.data?.rooms ?? [];

      state = state.copyWith(
        hotelrooms: hotelrooms,
        rooms: rooms,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load hotel rooms: $e',
      );
    }
  }

  /// Set sort option
  void setSortOption(String option) {
    state = state.copyWith(selectedSortOption: option);
  }

  /// Set room limits from home screen selection
  void setRoomLimits({required int maxRoomCount, required int totalGuests}) {
    state = state.copyWith(
      maxRoomCount: maxRoomCount,
      totalGuests: totalGuests,
    );
  }

  /// Get filtered room options based on sort option
  List<RoomOption> getFilteredRoomOptions(Room room) {
    if (state.selectedSortOption == 'all') {
      return room.roomOptions ?? [];
    }
    
    final options = room.roomOptions ?? [];
    
    switch (state.selectedSortOption) {
      case 'price_low_to_high':
        final sorted = List<RoomOption>.from(options);
        sorted.sort((a, b) {
          final priceA = a.fareDetail?.displayedBaseFare ?? 0.0;
          final priceB = b.fareDetail?.displayedBaseFare ?? 0.0;
          return priceA.compareTo(priceB);
        });
        return sorted;
      case 'price_high_to_low':
        final sorted = List<RoomOption>.from(options);
        sorted.sort((a, b) {
          final priceA = a.fareDetail?.displayedBaseFare ?? 0.0;
          final priceB = b.fareDetail?.displayedBaseFare ?? 0.0;
          return priceB.compareTo(priceA);
        });
        return sorted;
      default:
        return options;
    }
  }

  /// Add selected room option
  void addSelectedRoomOption(SelectedRoomOption option) {
    final updatedOptions = [...state.selectedRoomOptions, option];
    state = state.copyWith(selectedRoomOptions: updatedOptions);
  }

  /// Remove selected room option
  void removeSelectedRoomOption(String roomId) {
    final updatedOptions = state.selectedRoomOptions
        .where((option) => option.roomId != roomId)
        .toList();
    state = state.copyWith(selectedRoomOptions: updatedOptions);
  }

  /// Update room option quantity
  void updateRoomOptionQuantity(String roomId, int quantity) {
    final updatedOptions = state.selectedRoomOptions.map((option) {
      if (option.roomId == roomId) {
        return option.copyWith(quantity: quantity);
      }
      return option;
    }).toList();
    
    state = state.copyWith(selectedRoomOptions: updatedOptions);
  }

  /// Clear all selected room options
  void clearSelectedRoomOptions() {
    state = state.copyWith(selectedRoomOptions: []);
  }

  /// Get total selected rooms count
  int getTotalSelectedRoomsCount() {
    return state.selectedRoomOptions.fold(0, (sum, option) => sum + option.quantity);
  }

  /// Get total price for selected rooms
  double getTotalPrice() {
    return state.selectedRoomOptions.fold(0.0, (sum, option) => sum + (option.price * option.quantity));
  }

  /// Check if room selection is valid
  bool isSelectionValid() {
    final totalRooms = getTotalSelectedRoomsCount();
    return totalRooms > 0 && totalRooms <= state.maxRoomCount;
  }

  /// Refresh room data
  Future<void> refreshRoomData() async {
    await loadHotelRoomsFromJson();
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Room selection provider
final roomSelectionProvider = StateNotifierProvider<RoomSelectionNotifier, RoomSelectionState>(
  (ref) => RoomSelectionNotifier(),
);
