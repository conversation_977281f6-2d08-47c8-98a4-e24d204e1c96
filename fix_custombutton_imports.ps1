# PowerShell script to fix CustombuttonWidget import issues

# Get all Dart files in the project
$dartFiles = Get-ChildItem -Path "lib" -Recurse -Filter "*.dart"
$updatedFiles = 0

Write-Host "🔧 Fixing CustombuttonWidget imports..." -ForegroundColor Yellow

foreach ($file in $dartFiles) {
    $content = Get-Content $file.FullName -Raw
    $updated = $false
    
    # Fix CustombuttonWidget import path
    if ($content -match "package:kind_ali/widgets/custombutton_widget\.dart") {
        $content = $content -replace "package:kind_ali/widgets/custombutton_widget\.dart", "package:kind_ali/shared/presentation/widgets/custombutton_widget.dart"
        $updated = $true
    }
    
    if ($updated) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        $updatedFiles++
        Write-Host "Updated: $($file.Name)" -ForegroundColor Green
    }
}

Write-Host "Fixed CustombuttonWidget imports in $updatedFiles files" -ForegroundColor Yellow
