=== SHARED PREFERENCES DATA EXPORT ===

This file contains the exported SharedPreferences data for development/debugging purposes.

HOW TO VIEW STORED DATA:
1. Run the app: flutter run
2. Navigate to profile completion screen
3. Fill out and save profile information
4. The data will be automatically exported to console after saving
5. Copy the JSON output from console and paste it below for reference

ALTERNATIVE METHODS:
- Call ProfileCompletionService.exportStoredDataAsJson() anywhere in code
- Call ProfileCompletionService.getStoredDataAsString() for formatted text
- Call ProfileCompletionService.getAllSharedPreferencesData() for raw data

CURRENT DATA STRUCTURE EXAMPLE:
{
  "export_timestamp": "2024-01-01T00:00:00.000Z",
  "profile_data": {
    "fullName": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "dateOfBirth": "1990-01-01",
    "gender": "Male",
    "address": "123 Main St",
    "city": "New York",
    "country": "USA",
    "emergencyContact": null,
    "preferences": null,
    "profileImagePath": null,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "completion_status": "ProfileCompletionStatus.completeProfile",
  "completion_percentage": 80.0,
  "is_new_user": false,
  "has_minimum_required_data": true,
  "missing_required_fields": [],
  "missing_optional_fields": ["emergencyContact", "preferences"],
  "all_shared_preferences_data": {
    "profile_completion": "{\"fullName\":\"John Doe\",\"email\":\"<EMAIL>\",...}",
    "is_new_user": false
  }
}

=== END OF EXAMPLE ===

Instructions for developers:
1. To view current stored data, call ProfileCompletionService.exportStoredDataAsJson()
2. To view formatted text data, call ProfileCompletionService.getStoredDataAsString()
3. To clear all data, call ProfileCompletionService.clearAllData()
4. The data will be printed to the console during development
