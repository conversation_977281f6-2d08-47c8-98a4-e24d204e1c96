import 'package:dartz/dartz.dart';
import '../error/failures.dart';

/// Common type definitions used throughout the application

/// Result type for operations that can fail
typedef ResultFuture<T> = Future<Either<Failure, T>>;

/// Result type for synchronous operations that can fail
typedef ResultVoid = Either<Failure, void>;

/// Data map type for JSON operations
typedef DataMap = Map<String, dynamic>;
