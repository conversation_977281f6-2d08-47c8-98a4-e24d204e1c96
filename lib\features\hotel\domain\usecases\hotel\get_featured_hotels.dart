import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:kind_ali/core/error/failures.dart';
import 'package:kind_ali/shared/domain/usecases/base/usecase.dart';
import '../../entities/hotel_entity.dart';
import '../../repositories/hotel_repository.dart';

/// Use case for getting featured hotels
class GetFeaturedHotels implements UseCase<List<HotelEntity>, GetFeaturedHotelsParams> {
  final HotelRepository repository;

  GetFeaturedHotels(this.repository);

  @override
  Future<Either<Failure, List<HotelEntity>>> call(GetFeaturedHotelsParams params) async {
    return await repository.getPopularHotels(limit: params.limit);
  }
}

/// Parameters for GetFeaturedHotels use case
class GetFeaturedHotelsParams extends Equatable {
  final int limit;

  const GetFeaturedHotelsParams({this.limit = 10});

  @override
  List<Object> get props => [limit];
}
