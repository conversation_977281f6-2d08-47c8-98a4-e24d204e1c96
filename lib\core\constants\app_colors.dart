import 'package:flutter/material.dart';

class AppColors {
  static const primary = Color(0xFF003b95);
  static const secondary = Color(0xFF3333FF);
  static const tertiary = Color(0xFF0033CC);

  static const white = Color(0xFFFFFFFF);
  static const neutralLight = Color(0xFFF0F4FF);
  static const neutralDark = Color(0xFF1A1A40);
  static const accent = Color(0xFF00CCFF);

  static const background = white;
  static const surface = Color(0xFFF5F9FF);
  static const error = Color(0xFFD32F2F);
  static const success = Color(0xFF4CAF50);
  static const warning = Color(0xFFFFC107);

  static const text = neutralDark;
  static const textLight = Color(0xFF4D4D80);
  static const textOnPrimary = white;
  static const textOnSecondary = white;

  static const divider = Color(0xFFD6E0FF);
  static const shadow = Color(0x1A000080);

  static const MaterialColor primaryMaterialColor = MaterialColor(
    0xFF000080,
    <int, Color>{
      50: Color(0xFFE6E6F0),
      100: Color(0xFFBFBFD9),
      200: Color(0xFF9999C2),
      300: Color(0xFF7373AB),
      400: Color(0xFF4D4D94),
      500: Color(0xFF000080),
      600: Color(0xFF000073),
      700: Color(0xFF000066),
      800: Color(0xFF000059),
      900: Color(0xFF00004D),
    },
  );
}
